---
type: "always_apply"
---

# User Guidelines

## Contexto de Trabajo

- **Ambiente**: Windows 11, Visual Studio Code, Augment Code
- **Idioma conversaciones**: Español con justificaciones pros/cons
- **Idioma código**: Inglés para variables, funciones, comentarios, documentación técnica
- **Errores y trazas**: Mantener en idioma original

## Mentalidad Multi por Defecto

- **Multi-idioma**: Soporte i18n desde diseño
- **Multi-zona horaria**: UTC backend, conversión frontend
- **Multi-moneda**: Configuración flexible de divisas
- **Multi-tenant**: Aislamiento de datos por tenant_id
- **Excepción**: Solo si PRD especifica diferente

## Flujo de Trabajo con IA

- **Antes de codificar**: Plan → Riesgos → Rollback (alcance, archivos, DoD, impactos)
- **<PERSON> hay ambigüedad**: Preguntar antes de cambiar código
- **PRs atómicos**: 1 tema, ≤400 líneas, incluir tests/docs, CI debe pasar
- **Diff preview**: Listar archivos + cambios propuestos, confirmar antes de Next Edit

## Seguridad por Defecto

- **No secretos en código**: Usar variables de entorno, placeholders en .env.example
- **Dependencias justificadas**: Licencia, mantenimiento, overhead, alternativas
- **Secure-by-default**: Validar servidor, authN/authZ por recurso, no PII cliente/logs
- **Headers seguridad**: CSP/HSTS, cookies HttpOnly/SameSite, límites rate/size/timeouts

## Observabilidad

- **Cambios riesgosos**: Feature flags, logs/métricas básicas, plan rollback realista
- **Monitoreo**: Health checks, métricas de negocio, alertas automatizadas

## Precedencia y Resolución Conflictos

### Jerarquía Documentos

- **Orden**: PRD > Rules específicas > User Guidelines
- **Conflictos**: Informar para evaluar precedencia por proyecto
- **Sin PRD**: Hacer preguntas para clarificar especificaciones antes de proceder

### Resolución Conflictos Entre Rules

- **Multi-tenant vs API simplicity**: Multi-tenant prevalece salvo PRD especifique monolithic
- **Testing coverage vs Development speed**: Coverage mínima funcional requerida
- **Modularidad vs File limits**: División obligatoria 400+ líneas independiente complejidad

### Conflictos PRD vs Best Practices

- **PRD requiere tecnología insegura**: Proponer alternativa segura con justificación
- **PRD especifica anti-patterns**: Implementar solicitado pero documentar riesgos
- **PRD límites recursos vs escalabilidad**: Implementar con warnings capacidad futura
- **PRD contradice multi-tenant**: Confirmar explícitamente antes proceder single-tenant
