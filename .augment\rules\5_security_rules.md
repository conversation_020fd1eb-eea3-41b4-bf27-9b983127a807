---
type: "always_apply"
---

# Reglas de Seguridad

## Prevencion Inyecciones

### SQL Injection

- **Parameterized queries**: Placeholders ($1, $2) nunca concatenacion
- **Query builders**: ORM que escapa automaticamente
- **Stored procedures**: Para operaciones complejas
- **Input validation**: Estricta por tipo (email, UUID, alphanumeric)

### Validacion Entrada

- **Whitelist approach**: Solo caracteres/patrones validos
- **Regex patterns**: Email, telefono, UUID especificos
- **Type checking**: Verificar tipos antes procesamiento
- **Length limits**: Maximos apropiados por campo

## Autenticacion y Autorizacion

### Passwords Seguros

- **Politica robusta**: Min 12 chars, mayus/minus/nums/especiales
- **Hash seguro**: bcrypt salt rounds ≥12 o Argon2
- **Anti-common**: Lista passwords prohibidos
- **No reutilizacion**: Historial passwords previos

### Gestion Sesiones

- **Config segura**: secure=true, httpOnly=true, sameSite='strict'
- **Timeout**: 30 minutos inactividad
- **Rotacion ID**: En login exitoso
- **Invalidacion**: Logout y eventos seguridad

### Control Acceso

- **RBAC**: Roles permisos granulares
- **Menor privilegio**: Solo permisos necesarios
- **Verificacion propiedad**: Usuario solo sus recursos
- **Deny by default**: Sin permiso explicito = denegado

## Proteccion Datos

### Encriptacion

- **Transit**: HTTPS/TLS 1.3 obligatorio
- **Sensitive data**: AES-256-GCM PII/financiera
- **Key management**: Sistema separado claves
- **Hash vs encrypt**: Hash irreversible passwords, encrypt datos recuperables

### Headers Seguridad

- **X-Frame-Options**: DENY prevenir clickjacking
- **X-Content-Type-Options**: nosniff prevenir MIME sniffing
- **X-XSS-Protection**: 1; mode=block
- **Strict-Transport-Security**: HSTS includeSubDomains
- **Content-Security-Policy**: Restrictivo, fuentes confiables
- **Referrer-Policy**: strict-origin-when-cross-origin

## Prevencion XSS

### Sanitizacion Output

- **Context-aware encoding**: HTML, JS, CSS, URL segun contexto
- **DOMPurify**: Sanitizar HTML permitido
- **Template engines**: Auto-escaping habilitado
- **Tag whitelist**: Solo tags seguros contenido rico

### Input Validation

- **Escape especiales**: &, <, >, ", ', / contexto HTML
- **URL validation**: href y src attributes
- **CDATA sections**: Contenido XML/XHTML

## Rate Limiting y DoS

### Limites por Endpoint

- **API general**: 100 requests/15min
- **Login attempts**: 5 intentos/15min
- **Password reset**: 3 requests/hora
- **File upload**: Tamano y numero limitado

### Implementacion

- **IP + usuario**: Combinar ambos enfoques
- **Sliding window**: Mas preciso que fixed
- **Exponential backoff**: Incrementar tiempo espera
- **IP whitelist**: Internas bypass limites

## Auditoria y Logs

### Eventos Criticos Auditar

- **Auth**: Login exitoso/fallido, logout, cambio password
- **Authorization**: Accesos denegados, cambios permisos
- **Data**: Acceso, modificacion, exportacion sensible
- **Config**: Cambios sistema, feature flags
- **Financial**: Transacciones, precios
- **Security**: Injection, XSS, rate limit exceeded

### Estructura Logs

- **ID**: userId, tenantId, sessionId, IP, userAgent
- **Event**: eventType, eventCategory, eventSeverity, timestamp
- **Context**: resource, method, statusCode, responseTime
- **Data**: metadata, changes (before/after), success flag

### Implementacion Auditoria

- **Middleware global**: Captura automatica requests/responses
- **Service layer**: Logs cambios entidades
- **DB triggers**: Cambios directos base datos
- **Alertas automaticas**: Eventos criticos

## File Upload Security

### Upload Validation

- **Extension whitelist**: Solo permitidas (.jpg, .png, .pdf)
- **MIME validation**: Content-Type y magic numbers
- **Size limits**: Maximo apropiado (5MB tipico)
- **Virus scanning**: Antivirus archivos subidos
- **Storage isolation**: Por tenant, fuera web root

### File Naming

- **Nombres unicos**: UUID o timestamp + random
- **Path traversal**: Validar no contiene ../ o \
- **Extension norm**: Convertir lowercase
- **Segregation**: Directorio por tenant/usuario

## Monitoreo Security

### Metricas

- **Failed logins**: Por IP/usuario/tiempo
- **Unauthorized**: 403/401 por endpoint
- **Rate limits**: Endpoints mas atacados
- **Injection attempts**: SQL/XSS/Command detectados
- **Patterns**: Picos trafico, geo anomala

### Alertas

- **Criticas**: Multiple failed logins, injection, data breach
- **Warnings**: Rate limits, unauthorized patterns
- **Info**: Successful logins, config changes
- **Threshold**: Basado patrones historicos

### Log Retention

- **Criticos**: 2 anos minimo
- **Error**: 1 ano
- **Warning**: 6 meses
- **Info**: 3 meses
- **Compliance**: GDPR, SOX segun regulacion

## Dependencias

### Vulnerability Management

- **Scanning**: npm audit, Snyk, Dependabot automatico
- **Update policy**: Critical/high inmediato, medium/low programado
- **Version pinning**: Lock files builds reproducibles
- **CVE feeds**: Suscripcion advisories

### CI/CD Security

- **Security gates**: Bloquear deploy vulnerabilidades criticas
- **Dependency checks**: Cada build
- **Secret scanning**: Keys/passwords commits
- **SAST/DAST**: Analysis tools integrados

## Compliance

### GDPR/Privacy

- **Data minimization**: Solo datos necesarios
- **Purpose limitation**: Uso declarado unicamente
- **Consent**: Opt-in explicito, opt-out facil
- **Erasure**: Implementar data deletion
- **Portability**: Export datos usuario

### External Audit

- **Documentation**: Politicas, procedimientos, controles
- **Evidence**: Logs, screenshots, configuraciones
- **Penetration testing**: Anual o bi-anual
- **Compliance reports**: SOC2, ISO27001 industria

## Checklist Security

### Development

- Parameterized queries todas BD operations
- Input validation todos endpoints
- Output encoding contexto apropiado
- Security headers configurados
- HTTPS forced production

### Production

- Rate limiting activo endpoints criticos
- CORS policy restrictiva
- Vulnerability scanning automatizado
- Security event logging completo
- Incident response plan documentado
- Backup encrypted y tested
