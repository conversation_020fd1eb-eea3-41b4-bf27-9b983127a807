#!/usr/bin/env node

/**
 * Script para verificar la configuración de cSpell y probar el diccionario español
 * Uso: node scripts/cspell-check.js
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verificando configuración de cSpell...\n');

// Verificar archivos de configuración
const configFiles = [
  '.cspell.json',
  '.vscode/settings.json',
  'dictionaries/spanish.txt'
];

configFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} - Encontrado`);
  } else {
    console.log(`❌ ${file} - No encontrado`);
  }
});

// Leer configuración de cSpell
try {
  const cspellConfig = JSON.parse(fs.readFileSync('.cspell.json', 'utf8'));
  console.log('\n📋 Configuración cSpell:');
  console.log(`   Idiomas: ${cspellConfig.language}`);
  console.log(`   Diccionarios: ${cspellConfig.dictionaries.join(', ')}`);
  console.log(`   Palabras personalizadas: ${cspellConfig.words.length}`);
  
  // Verificar overrides
  if (cspellConfig.overrides && cspellConfig.overrides.length > 0) {
    console.log('\n🎯 Overrides configurados:');
    cspellConfig.overrides.forEach((override, index) => {
      console.log(`   ${index + 1}. ${override.filename} - Idioma: ${override.language}`);
    });
  }
} catch (error) {
  console.log('❌ Error leyendo .cspell.json:', error.message);
}

// Leer diccionario español
try {
  const spanishDict = fs.readFileSync('dictionaries/spanish.txt', 'utf8');
  const words = spanishDict.split('\n').filter(line => line.trim() && !line.startsWith('#'));
  console.log(`\n📚 Diccionario español: ${words.length} palabras`);
} catch (error) {
  console.log('❌ Error leyendo diccionario español:', error.message);
}

console.log('\n✨ Verificación completada!');
console.log('\n💡 Consejos:');
console.log('   - Reinicia VS Code para aplicar cambios');
console.log('   - Usa Ctrl+Shift+P > "cSpell: Reload Configuration"');
console.log('   - Para agregar palabras: Ctrl+Shift+P > "cSpell: Add Words to Dictionary"');
