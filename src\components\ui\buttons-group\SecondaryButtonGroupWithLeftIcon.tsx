import React from "react";

export default function SecondaryButtonGroupWithLeftIcon() {
  return (
    <div className="max-w-full pb-3 overflow-x-auto custom-scrollbar sm:pb-0">
      <div className="min-w-[393px]">
        <div className="inline-flex items-center shadow-theme-xs">
          <button
            type="button"
            className="inline-flex items-center gap-2 bg-transparent px-4 py-3 text-sm font-medium text-gray-800 ring-1 ring-inset ring-gray-300 transition first:rounded-l-lg last:rounded-r-lg hover:bg-gray-50 dark:bg-white/[0.03] dark:text-gray-200 dark:ring-gray-700 dark:hover:bg-white/[0.03]"
          >
            <span className="fill-gray-800 group-hover:fill-gray-800 dark:fill-gray-200 dark:group-hover:fill-gray-200">
              <svg
                className="fill-current"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M9.77644 3.24175C9.9172 3.17137 10.0829 3.17137 10.2236 3.24175L15.3708 5.81524L10.3354 8.33283C10.1243 8.43839 9.87577 8.43839 9.66463 8.33283L4.62931 5.81524L9.77644 3.24175ZM3.70215 7.02871V13.412C3.70215 13.6013 3.80915 13.7745 3.97855 13.8592L9.24968 16.4947L9.24967 9.78321C9.16279 9.75247 9.07733 9.71623 8.99383 9.67447L3.70215 7.02871ZM10.7497 16.495V9.78347C10.8368 9.75267 10.9225 9.71634 11.0062 9.67447L16.2979 7.02871V13.412C16.2979 13.6013 16.1909 13.7745 16.0215 13.8592L10.7497 16.495ZM9.41414 17.4826L9.10563 18.0997C9.66867 18.3812 10.3314 18.3812 10.8944 18.0997L16.6923 15.2008C17.3699 14.862 17.7979 14.1695 17.7979 13.412V6.58782C17.7979 5.83027 17.3699 5.13774 16.6923 4.79896L10.8944 1.9001C10.3314 1.61859 9.66868 1.61859 9.10563 1.9001L9.44103 2.57092L9.10563 1.9001L3.30774 4.79896C2.63016 5.13774 2.20215 5.83027 2.20215 6.58782V13.412C2.20215 14.1695 2.63016 14.862 3.30774 15.2008L9.10563 18.0997L9.41414 17.4826Z"
                  fill=""
                />
              </svg>
            </span>
            Button Text
          </button>
          <button
            type="button"
            className="-ml-px inline-flex items-center gap-2 bg-transparent px-4 py-3 text-sm font-medium text-gray-700 ring-1 ring-inset ring-gray-300 transition first:rounded-l-lg last:rounded-r-lg hover:bg-gray-50 hover:text-gray-800 dark:bg-transparent dark:text-gray-200 dark:ring-gray-700 dark:hover:bg-white/[0.03]"
          >
            <span className="fill-gray-800 group-hover:fill-gray-800 dark:fill-gray-200 dark:group-hover:fill-gray-200">
              <svg
                className="fill-current"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M9.77644 3.24175C9.9172 3.17137 10.0829 3.17137 10.2236 3.24175L15.3708 5.81524L10.3354 8.33283C10.1243 8.43839 9.87577 8.43839 9.66463 8.33283L4.62931 5.81524L9.77644 3.24175ZM3.70215 7.02871V13.412C3.70215 13.6013 3.80915 13.7745 3.97855 13.8592L9.24968 16.4947L9.24967 9.78321C9.16279 9.75247 9.07733 9.71623 8.99383 9.67447L3.70215 7.02871ZM10.7497 16.495V9.78347C10.8368 9.75267 10.9225 9.71634 11.0062 9.67447L16.2979 7.02871V13.412C16.2979 13.6013 16.1909 13.7745 16.0215 13.8592L10.7497 16.495ZM9.41414 17.4826L9.10563 18.0997C9.66867 18.3812 10.3314 18.3812 10.8944 18.0997L16.6923 15.2008C17.3699 14.862 17.7979 14.1695 17.7979 13.412V6.58782C17.7979 5.83027 17.3699 5.13774 16.6923 4.79896L10.8944 1.9001C10.3314 1.61859 9.66868 1.61859 9.10563 1.9001L9.44103 2.57092L9.10563 1.9001L3.30774 4.79896C2.63016 5.13774 2.20215 5.83027 2.20215 6.58782V13.412C2.20215 14.1695 2.63016 14.862 3.30774 15.2008L9.10563 18.0997L9.41414 17.4826Z"
                  fill=""
                />
              </svg>
            </span>
            Button Text
          </button>
          <button
            type="button"
            className="-ml-px inline-flex items-center gap-2 bg-transparent px-4 py-3 text-sm font-medium text-gray-700 ring-1 ring-inset ring-gray-300 transition first:rounded-l-lg last:rounded-r-lg hover:bg-gray-50 hover:text-gray-800 dark:bg-transparent dark:text-gray-200 dark:ring-gray-700 dark:hover:bg-white/[0.03]"
          >
            <span className="fill-gray-800 group-hover:fill-gray-800 dark:fill-gray-200 dark:group-hover:fill-gray-200">
              <svg
                className="fill-current"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M9.77644 3.24175C9.9172 3.17137 10.0829 3.17137 10.2236 3.24175L15.3708 5.81524L10.3354 8.33283C10.1243 8.43839 9.87577 8.43839 9.66463 8.33283L4.62931 5.81524L9.77644 3.24175ZM3.70215 7.02871V13.412C3.70215 13.6013 3.80915 13.7745 3.97855 13.8592L9.24968 16.4947L9.24967 9.78321C9.16279 9.75247 9.07733 9.71623 8.99383 9.67447L3.70215 7.02871ZM10.7497 16.495V9.78347C10.8368 9.75267 10.9225 9.71634 11.0062 9.67447L16.2979 7.02871V13.412C16.2979 13.6013 16.1909 13.7745 16.0215 13.8592L10.7497 16.495ZM9.41414 17.4826L9.10563 18.0997C9.66867 18.3812 10.3314 18.3812 10.8944 18.0997L16.6923 15.2008C17.3699 14.862 17.7979 14.1695 17.7979 13.412V6.58782C17.7979 5.83027 17.3699 5.13774 16.6923 4.79896L10.8944 1.9001C10.3314 1.61859 9.66868 1.61859 9.10563 1.9001L9.44103 2.57092L9.10563 1.9001L3.30774 4.79896C2.63016 5.13774 2.20215 5.83027 2.20215 6.58782V13.412C2.20215 14.1695 2.63016 14.862 3.30774 15.2008L9.10563 18.0997L9.41414 17.4826Z"
                  fill=""
                />
              </svg>
            </span>
            Button Text
          </button>
        </div>
      </div>
    </div>
  );
}
