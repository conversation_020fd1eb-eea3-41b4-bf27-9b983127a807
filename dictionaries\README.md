# Configuración de cSpell - Diccionarios

Este directorio contiene la configuración de diccionarios para cSpell, el corrector ortográfico utilizado en VS Code.

## Archivos

### `spanish.txt`
Diccionario personalizado con términos técnicos en español utilizados en la documentación del proyecto.

**Categorías incluidas:**
- Términos generales de documentación
- Términos de desarrollo y arquitectura  
- Términos técnicos específicos del proyecto
- Palabras comunes en documentación técnica

## Configuración cSpell

### Archivos de configuración

- **`.cspell.json`** - Configuración principal de cSpell
- **`.vscode/settings.json`** - Configuración específica de VS Code

### Idiomas configurados:
- **Inglés (en)** - Para código y términos técnicos
- **Español (es)** - Para documentación

### Diccionarios activos:
- `spanish` - Diccionario personalizado español
- `typescript` - Términos TypeScript
- `node` - Términos Node.js
- `software-terms` - Términos de software general

## Overrides por tipo de archivo

### Archivos Markdown (`.md`)
- **Idioma principal:** Español
- **Diccionarios:** spanish, software-terms, typescript, node
- **Archivos específicos:** ABOUT.md con configuración especial

### Archivos de código (`.ts`, `.tsx`, `.js`, `.jsx`)
- **Idioma principal:** Inglés
- **Diccionarios:** typescript, node, software-terms

### Archivos de reglas (`.augment/rules/*.md`)
- **Idioma principal:** Español
- **Diccionarios:** spanish, software-terms

## Uso

### Verificar configuración
```bash
node scripts/cspell-check.js
```

### Comandos VS Code útiles
- **Recargar configuración:** `Ctrl+Shift+P` > "cSpell: Reload Configuration"
- **Agregar palabra:** `Ctrl+Shift+P` > "cSpell: Add Words to Dictionary"
- **Verificar archivo:** `Ctrl+Shift+P` > "cSpell: Check Document"

### Agregar nuevas palabras

#### Método 1: Automático desde VS Code
1. Hacer clic derecho en la palabra marcada como error
2. Seleccionar "Add to dictionary"
3. Elegir el diccionario apropiado

#### Método 2: Manual
1. Abrir `dictionaries/spanish.txt`
2. Agregar la palabra en la categoría apropiada
3. Guardar el archivo
4. Recargar configuración de cSpell

## Estructura del diccionario español

```
# Términos generales de documentación
Técnico, Información, Descripción...

# Términos de desarrollo y arquitectura  
administrativo, moderno, responsivo...

# Términos técnicos específicos del proyecto
Componentes, Autenticación, Gráficos...

# Términos técnicos originales
Reglas, Codigo, Nomenclatura...
```

## Solución de problemas

### Palabras marcadas como errores
1. Verificar que el idioma esté configurado correctamente
2. Agregar la palabra al diccionario apropiado
3. Recargar configuración de cSpell

### Configuración no se aplica
1. Reiniciar VS Code
2. Verificar que los archivos de configuración estén en la raíz del proyecto
3. Ejecutar el script de verificación

### Falsos positivos en inglés
- Verificar que el archivo tenga la configuración de idioma correcta
- Los archivos de código deben usar inglés por defecto
- Los archivos de documentación pueden usar español

## Mantenimiento

### Limpieza periódica
- Revisar palabras duplicadas en el diccionario
- Organizar palabras por categorías
- Eliminar palabras obsoletas

### Actualización
- Agregar nuevos términos técnicos según aparezcan
- Mantener consistencia en la nomenclatura
- Documentar cambios importantes

---

**Nota:** Esta configuración está optimizada para proyectos que mezclan documentación en español con código en inglés, siguiendo las mejores prácticas de desarrollo internacional.
