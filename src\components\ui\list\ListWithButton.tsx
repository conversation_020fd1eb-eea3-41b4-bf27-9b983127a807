import React from "react";

export default function ListWithButton() {
  return (
    <div className="w-full overflow-hidden rounded-lg border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03] sm:w-[228px]">
      <ul className="flex flex-col">
        <li className="border-b border-gray-200 last:border-b-0 dark:border-gray-800">
          <button className="flex w-full items-center gap-3 px-3 py-2.5 text-sm font-medium text-gray-500 hover:bg-brand-50 hover:text-brand-500 dark:text-gray-400 dark:hover:bg-brand-500/[0.12] dark:hover:text-brand-400">
            <span>
              <svg
                className="fill-current"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M12.2989 1.12891C11.4706 1.12891 10.799 1.80033 10.7989 2.62867L10.7988 3.1264V3.12659L10.799 4.87507H6.14518C3.60237 4.87507 1.54102 6.93642 1.54102 9.47923V14.3207C1.54102 15.4553 2.46078 16.3751 3.59536 16.3751H6.14518H9.99935H16.2077C17.4503 16.3751 18.4577 15.3677 18.4577 14.1251V10.1251C18.4577 7.22557 16.1072 4.87507 13.2077 4.87507H12.299L12.2989 3.87651H13.7503C14.509 3.87651 15.124 3.26157 15.1242 2.50293C15.1243 1.74411 14.5092 1.12891 13.7503 1.12891H12.2989ZM3.04102 9.47923C3.04102 7.76485 4.4308 6.37507 6.14518 6.37507C7.85957 6.37507 9.24935 7.76485 9.24935 9.47923V14.8751H6.14518H3.59536C3.28921 14.8751 3.04102 14.6269 3.04102 14.3207V9.47923ZM10.7493 9.47923V14.8751H16.2077C16.6219 14.8751 16.9577 14.5393 16.9577 14.1251V10.1251C16.9577 8.054 15.2788 6.37507 13.2077 6.37507H9.54559C10.2933 7.19366 10.7493 8.28319 10.7493 9.47923Z"
                  fill=""
                />
              </svg>
            </span>

            <span> Inbox </span>
          </button>
        </li>

        <li className="border-b border-gray-200 last:border-b-0 dark:border-gray-800">
          <button className="flex w-full items-center gap-3 px-3 py-2.5 text-sm font-medium text-gray-500 hover:bg-brand-50 hover:text-brand-500 dark:text-gray-400 dark:hover:bg-brand-500/[0.12] dark:hover:text-brand-400">
            <span>
              <svg
                className="fill-current"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M4.98433 2.44399C3.11285 1.57147 1.15276 3.46979 1.96494 5.36824L3.82037 9.70527C3.90097 9.89367 3.90097 10.1069 3.82037 10.2953L1.96494 14.6323C1.15277 16.5307 3.11284 18.4291 4.98432 17.5565L16.8179 12.0395C18.5503 11.2319 18.5503 8.76865 16.8179 7.961L4.98433 2.44399ZM3.34404 4.77824C3.07331 4.14543 3.72667 3.51266 4.3505 3.80349L16.1841 9.32051C16.7615 9.58973 16.7616 10.4108 16.1841 10.68L4.3505 16.197C3.72667 16.4879 3.07331 15.8551 3.34404 15.2223L5.19947 10.8853C5.21895 10.8397 5.23687 10.7937 5.25321 10.7473L9.11736 10.7473C9.53157 10.7473 9.86736 10.4115 9.86736 9.99726C9.86736 9.58304 9.53157 9.24726 9.11736 9.24726L5.25108 9.24726C5.23531 9.20287 5.21811 9.15885 5.19947 9.11528L3.34404 4.77824Z"
                  fill=""
                />
              </svg>
            </span>

            <span> Sent </span>
          </button>
        </li>

        <li className="border-b border-gray-200 last:border-b-0 dark:border-gray-800">
          <button className="flex w-full items-center gap-3 px-3 py-2.5 text-sm font-medium text-gray-500 hover:bg-brand-50 hover:text-brand-500 dark:text-gray-400 dark:hover:bg-brand-500/[0.12] dark:hover:text-brand-400">
            <span>
              <svg
                className="fill-current"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M3.04102 7.06206V14.375C3.04102 14.6511 3.26487 14.875 3.54102 14.875H16.4577C16.7338 14.875 16.9577 14.6511 16.9577 14.375V7.06245L11.1436 11.1168C10.4563 11.5961 9.543 11.5961 8.85565 11.1168L3.04102 7.06206ZM16.9577 5.19262C16.9577 5.19341 16.9577 5.1942 16.9577 5.19498V5.20026C16.9565 5.22216 16.9453 5.24239 16.9272 5.25501L10.2856 9.88638C10.1138 10.0062 9.88547 10.0062 9.71364 9.88638L3.07181 5.25485C3.05269 5.24151 3.04129 5.21967 3.04128 5.19636C3.04127 5.15695 3.07321 5.125 3.11262 5.125H16.8864C16.9245 5.125 16.9557 5.15494 16.9577 5.19262ZM18.4577 5.21428V14.375C18.4577 15.4796 17.5623 16.375 16.4577 16.375H3.54102C2.43645 16.375 1.54102 15.4796 1.54102 14.375V5.19498C1.54102 5.1852 1.5412 5.17546 1.54157 5.16577C1.55785 4.31209 2.25497 3.625 3.11262 3.625H16.8864C17.7542 3.625 18.4577 4.32843 18.4578 5.19622C18.4578 5.20225 18.4578 5.20826 18.4577 5.21428Z"
                  fill=""
                />
              </svg>
            </span>

            <span> Drafts </span>
          </button>
        </li>

        <li className="border-b border-gray-200 last:border-b-0 dark:border-gray-800">
          <button className="flex w-full items-center gap-3 px-3 py-2.5 text-sm font-medium text-gray-500 hover:bg-brand-50 hover:text-brand-500 dark:text-gray-400 dark:hover:bg-brand-500/[0.12] dark:hover:text-brand-400">
            <span>
              <svg
                className="fill-current"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M6.54191 3.7915C6.54191 2.54886 7.54927 1.5415 8.79191 1.5415H11.2086C12.4512 1.5415 13.4586 2.54886 13.4586 3.7915V4.0415H15.6257H16.6665C17.0807 4.0415 17.4165 4.37729 17.4165 4.7915C17.4165 5.20572 17.0807 5.5415 16.6665 5.5415H16.3757V8.24638V13.2464V16.2082C16.3757 17.4508 15.3683 18.4582 14.1257 18.4582H5.87565C4.63301 18.4582 3.62565 17.4508 3.62565 16.2082V13.2464V8.24638V5.5415H3.33398C2.91977 5.5415 2.58398 5.20572 2.58398 4.7915C2.58398 4.37729 2.91977 4.0415 3.33398 4.0415H4.37565H6.54191V3.7915ZM14.8757 13.2464V8.24638V5.5415H13.4586H12.7086H7.29191H6.54191H5.12565V8.24638V13.2464V16.2082C5.12565 16.6224 5.46144 16.9582 5.87565 16.9582H14.1257C14.5399 16.9582 14.8757 16.6224 14.8757 16.2082V13.2464ZM8.04191 4.0415H11.9586V3.7915C11.9586 3.37729 11.6228 3.0415 11.2086 3.0415H8.79191C8.3777 3.0415 8.04191 3.37729 8.04191 3.7915V4.0415ZM8.33398 7.99984C8.7482 7.99984 9.08398 8.33562 9.08398 8.74984V13.7498C9.08398 14.1641 8.7482 14.4998 8.33398 14.4998C7.91977 14.4998 7.58398 14.1641 7.58398 13.7498V8.74984C7.58398 8.33562 7.91977 7.99984 8.33398 7.99984ZM12.4173 8.74984C12.4173 8.33562 12.0815 7.99984 11.6673 7.99984C11.2531 7.99984 10.9173 8.33562 10.9173 8.74984V13.7498C10.9173 14.1641 11.2531 14.4998 11.6673 14.4998C12.0815 14.4998 12.4173 14.1641 12.4173 13.7498V8.74984Z"
                  fill=""
                />
              </svg>
            </span>

            <span> Trash </span>
          </button>
        </li>

        <li className="border-b border-gray-200 last:border-b-0 dark:border-gray-800">
          <button
            disabled
            className="flex w-full items-center gap-3 px-3 py-2.5 text-sm font-medium text-gray-500 disabled:opacity-50 dark:text-gray-400"
          >
            <span>
              <svg
                className="fill-current"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M3.04102 9.99984C3.04102 6.15686 6.15637 3.0415 9.99935 3.0415C13.8423 3.0415 16.9577 6.15686 16.9577 9.99984C16.9577 13.8428 13.8423 16.9582 9.99935 16.9582C6.15637 16.9582 3.04102 13.8428 3.04102 9.99984ZM9.99935 1.5415C5.32794 1.5415 1.54102 5.32843 1.54102 9.99984C1.54102 14.6712 5.32794 18.4582 9.99935 18.4582C14.6708 18.4582 18.4577 14.6712 18.4577 9.99984C18.4577 5.32843 14.6708 1.5415 9.99935 1.5415ZM8.99861 6.27073C8.99861 6.82302 9.44632 7.27073 9.99861 7.27073H9.99961C10.5519 7.27073 10.9996 6.82302 10.9996 6.27073C10.9996 5.71845 10.5519 5.27073 9.99961 5.27073H9.99861C9.44632 5.27073 8.99861 5.71845 8.99861 6.27073ZM9.99942 14.601C9.58521 14.601 9.24942 14.2652 9.24942 13.851L9.24942 9.12059C9.24942 8.70637 9.58521 8.37059 9.99942 8.37059C10.4136 8.37059 10.7494 8.70637 10.7494 9.12059V13.851C10.7494 14.2652 10.4136 14.601 9.99942 14.601Z"
                  fill=""
                />
              </svg>
            </span>

            <span> Spam </span>
          </button>
        </li>
      </ul>
    </div>
  );
}
