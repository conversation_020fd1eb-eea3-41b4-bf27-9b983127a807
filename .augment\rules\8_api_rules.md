---
type: "always_apply"
---

# Reglas de API Design

## Principios REST

### URL Structure

- **Recursos sustantivos**: /api/users, /api/orders (nunca verbos /getUsers)
- **Jerarquia logica**: /api/v1/users/:id/posts sub-recursos
- **Convenciones**: Plural colecciones, singular recurso especifico
- **Versionado**: URL /api/v1/ metodo preferido

### HTTP Verbs

- **GET**: Obtener recursos (idempotente, no modifica)
- **POST**: Crear nuevo recurso
- **PUT**: Actualizacion completa recurso
- **PATCH**: Actualizacion parcial
- **DELETE**: Eliminar recurso

## Request/Response Format

### Request Structure

- **Data wrapper**: Objeto "data" payload principal
- **Validation**: Campos requeridos especificados
- **Content-Type**: application/json APIs modernas

### Response Consistency

- **Success**: success flag, data, metadata (timestamp, version, requestId)
- **Error**: success false, error object code/message/details array
- **Metadata**: Timestamp, path, requestId debugging
- **Format**: Consistente todos endpoints

## Status Codes Error Handling

### Primary Status Codes

- **2xx Success**: 200 (GET), 201 (POST), 204 (DELETE)
- **4xx Client**: 400 (bad request), 401 (unauthorized), 403 (forbidden), 404 (not found), 422 (validation)
- **5xx Server**: 500 (internal), 503 (service unavailable)
- **Rate limiting**: 429 headers X-RateLimit-*

### Error Structure

- **Structured**: Code, message, details array field-level errors
- **Debug info**: RequestId, timestamp, path troubleshooting
- **User-friendly**: Diferentes niveles detalle audience

## Pagination Filtering

### Pagination

- **Query params**: page, limit, sort, filter, search
- **Response meta**: pagination object page/limit/total/totalPages/hasNext/hasPrev
- **Links**: first, prev, next, last URLs navegacion
- **Limits**: Default 20, maximum 100 items pagina

### Filtering Search

- **Simple**: ?status=active&role=admin
- **Advanced**: ?filter[age][gte]=18&filter[status][in]=pending,processing
- **Search**: ?search=laptop busqueda texto
- **Sorting**: ?sort=-createdAt (- descendente)

## Authentication Security

### Bearer Token

- **Header**: Authorization: Bearer [token]
- **JWT validation**: Verificar signature, expiracion, claims
- **Error responses**: 401 no auth, 403 no permission
- **Token refresh**: Mecanismo renovar tokens expirados

### Rate Limiting

- **Headers**: X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Reset
- **Limits endpoint**: Diferentes segun criticidad
- **Exceed response**: 429 retryAfter seconds
- **IP whitelist**: IPs internas bypass

## CORS Validation

### CORS Configuration

- **Origin whitelist**: Solo dominios autorizados
- **Credentials**: true cookies/auth headers
- **Methods**: Especificar permitidos
- **Headers**: Allowed exposed apropiados

### Input Validation

- **Schema**: JSON Schema request validation
- **Field validation**: Required, format, length, pattern
- **Error details**: Array field-level validation errors
- **Sanitization**: Input cleaning antes processing

## Documentation Testing

### OpenAPI Specification

- **Complete structure**: info, servers, paths, components, schemas
- **Examples**: Request/response cada endpoint
- **Authentication**: Security schemes documentados
- **Versioning**: Multiple versions misma spec

### API Testing

- **Postman collections**: Organized modules auth setup
- **Environment vars**: base_url, access_token entornos
- **Test scripts**: Automated assertions Postman
- **Integration tests**: Happy path, error cases, edge cases

## Webhooks Integration

### Webhook Design

- **Event-based**: Notificar subscribers eventos importantes
- **Payload**: event, data, timestamp, signature
- **Security**: HMAC signature verificar authenticity
- **Retry logic**: Automatic retry failed deliveries
- **Timeouts**: Reasonable webhook calls

### API Monitoring

- **Health checks**: /health endpoint dependency status
- **Metrics**: Response time, error rate, throughput
- **Logging**: Structured request/response details
- **Alerting**: Automated alerts high error rates

## Performance Security

### Performance

- **Caching headers**: Cache-Control, ETag GET requests
- **Compression**: Gzip responses
- **Pagination**: Siempre collections grandes
- **Field selection**: ?fields=id,name optimization

### Security

- **HTTPS only**: Forzar SSL production
- **Input validation**: Validate sanitize todos inputs
- **SQL injection**: Parameterized queries siempre
- **XSS protection**: Proper encoding outputs

### Maintainability

- **Consistent naming**: snake_case o camelCase consistente
- **Version deprecation**: Clear timeline
- **Backward compatibility**: Avoid breaking changes minor versions
- **Documentation**: Updated cada release

## API Checklist

### Development

- Principios REST implementados
- Status codes apropiados scenarios
- Input validation comprehensive
- Error handling consistente
- Authentication authorization segura

### Production

- Rate limiting configurado
- CORS policy restrictiva
- HTTPS enforced
- Monitoring logging activo
- Documentation actualizada accesible
- Backup disaster recovery tested

### Integration Standards

- OpenAPI spec completa actualizada
- Postman collections organized
- Webhook security HMAC signatures
- Health endpoints monitoring ready
- Performance optimization implemented
