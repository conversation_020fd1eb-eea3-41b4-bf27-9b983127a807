import React from "react";

export default function PaymentMethod() {
  return (
    <div className="mb-6 rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03]">
      <div className="flex flex-col justify-between gap-5 px-6 py-5 sm:flex-row sm:items-start">
        <div>
          <h3 className="text-base font-medium text-gray-800 dark:text-white/90">
            Payment Methods
          </h3>
        </div>
        <div>
          <button
            type="button"
            className="shadow-theme-xs flex w-full justify-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="21"
              height="20"
              viewBox="0 0 21 20"
              fill="none"
            >
              <path
                d="M5.5 10.0002H15.5006M10.5002 5V15.0006"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            Add New Card
          </button>
        </div>
      </div>
      <div className="border-t border-gray-200 p-4 sm:p-6 dark:border-gray-800">
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 xl:grid-cols-3">
          {/* <!-- Master --> */}
          <div className="flex gap-5 rounded-xl border border-gray-200 p-3 pr-5 dark:border-gray-800">
            <div className="inline-flex h-13 w-13 shrink-0 items-center justify-center rounded-lg border border-gray-200 dark:border-gray-800">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="33"
                height="32"
                viewBox="0 0 33 32"
                fill="none"
              >
                <circle cx="10.5" cy="16" r="9" fill="#E80B26" />
                <circle cx="22.5" cy="16" r="9" fill="#F59D31" />
                <path
                  d="M16.5 22.7085C18.3413 21.0605 19.5 18.6658 19.5 16.0002C19.5 13.3347 18.3413 10.9399 16.5 9.29199C14.6587 10.9399 13.5 13.3347 13.5 16.0002C13.5 18.6658 14.6587 21.0605 16.5 22.7085Z"
                  fill="#FC6020"
                />
              </svg>
            </div>
            <div>
              <h3 className="mb-2 flex items-center gap-2 text-gray-800 dark:text-white/90">
                Mastercard
                <span className="bg-success-50 text-success-600 dark:bg-success-500/15 dark:text-success-500 inline-flex items-center justify-center gap-1 rounded-full py-0.5 pr-2.5 pl-2 text-sm font-medium">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="13"
                    height="12"
                    viewBox="0 0 13 12"
                    fill="none"
                  >
                    <path
                      d="M9.875 3.646L5.16657 8.35442L3.125 6.31285"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  Default
                </span>
              </h3>
              <div className="flex flex-wrap items-center justify-between gap-5">
                <p className="text-sm font-normal text-gray-400 dark:text-gray-400">
                  **** **** **** 9029
                </p>
                <p className="text-sm font-normal text-gray-400 dark:text-gray-400">
                  Expiry 01/24
                </p>
              </div>
              <div className="mt-4 flex flex-wrap gap-3">
                <button className="shadow-theme-xs inline-flex h-6 items-center justify-center rounded-md border border-gray-300 px-3 py-1 text-xs font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200">
                  Edit
                </button>
                <button className="shadow-theme-xs inline-flex h-6 items-center justify-center rounded-md border border-gray-300 px-3 py-1 text-xs font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200">
                  Delete
                </button>
              </div>
            </div>
          </div>

          {/* <!-- Visa --> */}
          <div className="flex gap-5 rounded-xl border border-gray-200 p-3 pr-5 dark:border-gray-800">
            <div className="inline-flex h-13 w-13 shrink-0 items-center justify-center rounded-lg border border-gray-200 dark:border-gray-800">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="33"
                height="18"
                viewBox="0 0 33 18"
                fill="none"
              >
                <g clipPath="url(#clip0_5607_13291)">
                  <path
                    d="M21.2243 3.90918C18.9651 3.90918 16.9462 5.06569 16.9462 7.20245C16.9462 9.65285 20.5268 9.82209 20.5268 11.0531C20.5268 11.5715 19.9254 12.0355 18.8981 12.0355C17.4403 12.0355 16.3507 11.3871 16.3507 11.3871L15.8844 13.5434C15.8844 13.5434 17.1396 14.091 18.8061 14.091C21.2762 14.091 23.2198 12.8777 23.2198 10.7045C23.2198 8.11511 19.6243 7.95089 19.6243 6.80831C19.6243 6.4022 20.118 5.95732 21.1423 5.95732C22.298 5.95732 23.2409 6.42885 23.2409 6.42885L23.6972 4.34631C23.6972 4.34631 22.6712 3.90918 21.2243 3.90918ZM0.554718 4.06638L0.5 4.38071C0.5 4.38071 1.45047 4.55249 2.3065 4.89522C3.40871 5.28816 3.48725 5.51692 3.67287 6.22747L5.69567 13.9289H8.40731L12.5848 4.06638H9.87935L7.19509 10.7719L6.09978 5.08798C5.99931 4.43747 5.49047 4.06638 4.86767 4.06638H0.554718ZM13.6726 4.06638L11.5503 13.9289H14.1301L16.245 4.06634L13.6726 4.06638ZM28.0612 4.06638C27.4391 4.06638 27.1095 4.39529 26.8676 4.97009L23.088 13.9289H25.7934L26.3168 12.4357H29.6128L29.9311 13.9289H32.3182L30.2357 4.06638H28.0612ZM28.413 6.73093L29.2149 10.4318H27.0665L28.413 6.73093Z"
                    fill="#1434CB"
                  />
                </g>
                <defs>
                  <clipPath id="clip0_5607_13291">
                    <rect
                      width="32"
                      height="17.4545"
                      fill="white"
                      transform="translate(0.5 0.272949)"
                    />
                  </clipPath>
                </defs>
              </svg>
            </div>
            <div>
              <h3 className="mb-2 text-gray-800 dark:text-white/90">Visa</h3>
              <div className="flex flex-wrap items-center justify-between gap-5">
                <p className="text-sm font-normal text-gray-400 dark:text-gray-400">
                  **** **** **** 4328
                </p>
                <p className="text-sm font-normal text-gray-400 dark:text-gray-400">
                  Expiry 01/25
                </p>
              </div>
              <div className="mt-4 flex flex-wrap gap-3">
                <button className="shadow-theme-xs inline-flex h-6 items-center justify-center rounded-md border border-gray-300 px-3 py-1 text-xs font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200">
                  Make Default
                </button>
                <button className="shadow-theme-xs inline-flex h-6 items-center justify-center rounded-md border border-gray-300 px-3 py-1 text-xs font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200">
                  Edit
                </button>
                <button className="shadow-theme-xs inline-flex h-6 items-center justify-center rounded-md border border-gray-300 px-3 py-1 text-xs font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200">
                  Delete
                </button>
              </div>
            </div>
          </div>

          {/* <!-- Paypal --> */}
          <div className="flex gap-5 rounded-xl border border-gray-200 p-3 pr-5 dark:border-gray-800">
            <div className="inline-flex h-13 w-13 shrink-0 items-center justify-center rounded-lg border border-gray-200 dark:border-gray-800">
              <svg
                width="33"
                height="32"
                viewBox="0 0 33 32"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect x="0.5" width="32" height="32" rx="16" fill="#1B4BF1" />
                <path
                  opacity="0.5"
                  d="M23.2413 12.5812C23.457 11.1743 23.2413 10.2365 22.4861 9.37074C21.6589 8.39679 20.1486 8 18.2066 8H12.6326C12.237 8 11.9133 8.28858 11.8414 8.68537L9.50392 23.4749C9.46796 23.7635 9.68373 24.016 9.97142 24.016H13.4237L13.172 25.5311C13.136 25.7836 13.3159 26 13.6035 26H16.5164C16.8761 26 17.1637 25.7475 17.1997 25.4228L17.8111 21.5992C17.847 21.2745 18.1707 21.022 18.4943 21.022H18.9259C21.7309 21.022 23.9605 19.8677 24.6078 16.5491C24.8595 15.1784 24.7516 13.988 24.0324 13.1944C23.8166 12.9419 23.5649 12.7615 23.2413 12.5812Z"
                  fill="white"
                />
                <path
                  d="M23.2413 12.5812C23.457 11.1743 23.2413 10.2365 22.4861 9.37074C21.6589 8.39679 20.1486 8 18.2066 8H12.6326C12.237 8 11.9133 8.28858 11.8414 8.68537L9.50392 23.4749C9.46796 23.7635 9.68373 24.016 9.97142 24.016H13.4237L14.2509 18.6774C14.3228 18.2806 14.6464 17.992 15.042 17.992H16.6962C19.9328 17.992 22.4501 16.6934 23.1693 12.8697C23.2053 12.7976 23.2053 12.6894 23.2413 12.5812Z"
                  fill="white"
                />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M24.1967 12.8768C24.1953 12.884 24.1929 12.8968 24.1892 12.9173C24.1846 12.9428 24.1746 12.998 24.1595 13.0571C24.1535 13.0803 24.1458 13.1083 24.1356 13.14C23.7299 15.2071 22.8141 16.7099 21.4579 17.6785C20.1051 18.6446 18.4356 18.992 16.6962 18.992H15.214L14.2807 25.016H9.97134C9.05713 25.016 8.40434 24.2113 8.51152 23.3513L8.51354 23.335L10.8554 8.51812L10.8574 8.50702C11.0085 7.67314 11.7152 7 12.6325 7H18.2066C20.2089 7 22.1155 7.39441 23.2438 8.71829C23.7128 9.25723 24.0388 9.85067 24.2014 10.5451C24.3613 11.2282 24.3494 11.9515 24.2296 12.7328L24.2168 12.8163L24.1967 12.8768ZM22.486 9.37074C21.6589 8.39679 20.1485 8 18.2066 8H12.6325C12.2369 8 11.9133 8.28858 11.8413 8.68537L9.50384 23.4749C9.46788 23.7635 9.68365 24.016 9.97134 24.016H13.4237L14.2508 18.6774C14.3227 18.2806 14.6464 17.992 15.0419 17.992H16.6962C19.9327 17.992 22.45 16.6934 23.1693 12.8697C23.1887 12.8307 23.1977 12.7811 23.2075 12.7266C23.2158 12.6804 23.2247 12.6308 23.2412 12.5812C23.4569 11.1743 23.2412 10.2365 22.486 9.37074Z"
                  fill="#1B4BF1"
                />
                <path
                  d="M15.2218 12.6172C15.2578 12.3647 15.5814 12.0401 15.9051 12.0401H20.2924C20.7959 12.0401 21.2993 12.0762 21.7309 12.1483C22.1265 12.2204 22.8457 12.4008 23.2053 12.6172C23.4211 11.2104 23.2053 10.2725 22.4501 9.40681C21.6589 8.39679 20.1486 8 18.2066 8H12.6326C12.237 8 11.9134 8.28858 11.8414 8.68537L9.50392 23.4749C9.46796 23.7635 9.68373 24.016 9.97142 24.016H13.4237L15.2218 12.6172Z"
                  fill="white"
                />
              </svg>
            </div>
            <div>
              <h3 className="mb-2 text-gray-800 dark:text-white/90">Paypal</h3>
              <div>
                <p className="text-sm font-normal text-gray-400 dark:text-gray-400">
                  <EMAIL>
                </p>
              </div>
              <div className="mt-4 flex flex-wrap gap-3">
                <button className="shadow-theme-xs inline-flex h-6 items-center justify-center rounded-md border border-gray-300 px-3 py-1 text-xs font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200">
                  Make Default
                </button>
                <button className="shadow-theme-xs inline-flex h-6 items-center justify-center rounded-md border border-gray-300 px-3 py-1 text-xs font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200">
                  Edit
                </button>
                <button className="shadow-theme-xs inline-flex h-6 items-center justify-center rounded-md border border-gray-300 px-3 py-1 text-xs font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200">
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
