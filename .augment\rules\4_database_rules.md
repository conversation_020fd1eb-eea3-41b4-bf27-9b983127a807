---
type: "always_apply"
---

# Reglas de Base de Datos

## Estrategia Multi-Tenant

### Principio: Tabla Compartida con tenant_id

- **99% proyectos SaaS**: Opcion correcta inicial
- **Beneficios**: Mantenimiento simple, escalable miles tenants, costo-efectivo
- **Escalabilidad**: Hasta schema aislado cuando sea necesario

### Estructura Base Obligatoria

Todas las tablas deben incluir:

- **id**: UUID primary key con gen_random_uuid()
- **tenant_id**: UUID NOT NULL references tenants(id)
- **created_at/updated_at**: Timestamps obligatorios
- **deleted_at**: Para soft delete
- **created_by/updated_by**: Auditoria usuarios

### Tabla Tenants Esencial

- **Basico**: name, slug, domain
- **Plan**: plan, status, max_users, storage_limit_gb
- **Fechas**: created_at, trial_ends_at, suspended_at
- **Metadata**: settings, features (JSONB)
- **Migracion**: storage_strategy, shard_id (futuro)

## Nomenclatura Base de Datos

### Reglas Fundamentales

- **Todo en ingles** sin excepciones
- **snake_case** para tablas, columnas, indices
- **Nombres descriptivos** sin abreviaciones
- **Tablas plural**, columnas singular

### Convenciones Especificas

- **Tablas**: users, products, order_items
- **Relaciones M:N**: user_roles, product_categories
- **IDs**: id, user_id, parent_id, external_id
- **Booleanos**: is_active, has_premium, can_edit
- **Contadores**: login_count, retry_count (_count sufijo)
- **Fechas**: created_at, expires_at (_at timestamps,_date fechas)
- **Dinero**: amount, price, total
- **Porcentajes**: discount_percent, tax_rate

### Indices y Constraints

- **Simple**: idx_tabla_columna
- **Compuesto**: idx_tabla_col1_col2
- **Unico**: unq_tabla_columna
- **Foreign key**: fk_tabla_columna
- **Check**: chk_tabla_descripcion

## Seguridad Multi-Tenant

### Row Level Security (RLS)

- **Obligatorio**: Habilitar en todas tablas tenant data
- **Politica**: tenant_id = current_setting('app.current_tenant')::uuid
- **Roles**: app_user (sin bypass), app_admin (con bypass migraciones)
- **Helper**: set_current_tenant() funcion

### Implementacion Aplicacion

- **Middleware tenant**: Extraer de subdomain/header/JWT
- **Validacion**: Verificar existencia y status activo
- **Contexto RLS**: Ejecutar set_current_tenant() cada request
- **Repository**: BaseRepository siempre incluye tenant_id

### Testing Seguridad

- **Aislamiento**: Tests verifican RLS previene cross-tenant
- **Auditoria queries**: Validar todos incluyen tenant_id
- **Doble verificacion**: ID y tenant en queries

## Optimizacion Multi-Tenant

### Regla Oro: tenant_id SIEMPRE Primero

- **Indices compuestos**: tenant_id primera columna siempre
- **Foreign keys**: Con tenant_id incluido
- **Busquedas**: Filtrar por tenant_id antes otros campos
- **Especializados**: Indices para tenants grandes (>1000 records)

### Queries Optimizadas

- **Paginacion**: Cursor-based, nunca OFFSET
- **N+1 Prevention**: JOIN o batch loading con tenant_id
- **Batch operations**: ANY() con arrays IDs
- **Prepared statements**: Para queries frecuentes

### Connection Pooling

- **Compartido**: Tenants pequenos (5-20 conexiones)
- **Dedicado**: Tenants enterprise (2-10 conexiones)
- **Auto-scaling**: Basado metricas uso
- **Timeouts**: statement/query/connection apropiados

### Cache Multi-Tenant

- **Namespace**: tenant:tenantId:type:id
- **TTL por tipo**: user(1h), product(2h), order(5min), config(1sem)
- **Warming**: Pre-cargar datos frecuentes tenants activos
- **Invalidacion**: Por tenant o keys especificas

## Mantenimiento y Monitoreo

### Identificacion Problemas

- **Indices no usados**: idx_scan < 10 eliminar
- **Indices duplicados**: Mismas columnas mismo orden
- **Bloat**: avg_leaf_density < 70% necesita REINDEX
- **Queries lentas**: mean_exec_time > 1000ms por tenant

### Mantenimiento Automatico

- **Autovacuum agresivo**: vacuum_scale_factor 0.05
- **ANALYZE frecuente**: Despues grandes cambios
- **REINDEX programado**: Mensual para indices bloat
- **Archivado**: Datos antiguos automatico

### Metricas por Tenant

- **Recursos**: Usuarios, ordenes, productos count
- **Performance**: Query time promedio, cache hit ratio
- **Crecimiento**: Registros nuevos por periodo
- **Actividad**: Logins, transacciones, features uso

### Health Checks

- **RLS status**: Verificar rowsecurity = true
- **Tenants problematicos**: Queries lentas identificados
- **Indices uso**: Estadisticas idx_scan
- **Pools conexion**: Activas vs disponibles

## Escalabilidad

### Senales Migracion Schema Aislado

- **Revenue**: >$5000/mes tenant
- **Data size**: >50GB
- **Users**: >500 activos
- **Compliance**: Requisitos especiales
- **Performance**: >500ms queries consistente

### Proceso Migracion Zero-Downtime

1. **Prep**: Schema dedicado, estructura copia
2. **Initial**: Migrar datos existentes
3. **Sync**: CDC triggers temporales
4. **Switch**: Cambio atomico storage_strategy
5. **Cleanup**: Eliminar triggers, datos compartidos

### Particionamiento

- **Hash tenant_id**: Distribucion uniforme carga
- **Particiones equilibradas**: Tamano similar
- **Indices replicados**: Estructura consistente
- **Cross-partition**: Minimizar con diseno

## Anti-Patrones Evitar

### Queries Problematicos

- **OFFSET alto**: Usar cursor pagination
- **SELECT * sin tenant_id**: Vulnerabilidad seguridad
- **N+1 queries**: JOIN o batch loading
- **Cross-tenant JOINs**: Sin tenant_id condiciones

### Configuracion

- **Pool muy pequeno**: Bottleneck conexiones
- **Timeouts muy altos**: Queries runaway
- **Cache sin namespace**: Data leakage tenants
- **Indices sin tenant_id**: Performance pobre

## Checklist Optimizacion

### Indices

- tenant_id primera columna TODOS indices
- Foreign keys incluyen tenant_id
- Indices parciales datos activos
- REINDEX programado mensual

### Queries  

- Cursor pagination implementada
- Batch loading N+1 prevention
- Prepared statements queries frecuentes
- EXPLAIN ANALYZE regular

### Seguridad

- RLS habilitado todas tablas
- Tests aislamiento automatizados
- Auditoria queries tenant_id
- Doble validacion aplicacion + BD

### Monitoreo

- Metricas por tenant
- Alertas queries lentas
- Health checks automaticos
- Estadisticas uso recursos
