{"version": "0.2", "language": "en,es", "dictionaries": ["spanish", "typescript", "node", "software-terms"], "dictionaryDefinitions": [{"name": "spanish", "path": "./dictionaries/spanish.txt"}], "ignorePaths": ["node_modules/**", "dist/**", "build/**", ".git/**"], "overrides": [{"filename": "**/.augment/rules/*.md", "language": "es", "dictionaries": ["spanish", "software-terms"]}, {"filename": "**/ABOUT.md", "language": "es", "dictionaries": ["spanish", "software-terms", "typescript", "node"]}, {"filename": "**/*spanish*.txt", "language": "es", "dictionaries": ["spanish"]}], "words": ["Reg<PERSON>", "Codigo", "Nomenclatura", "Convenciones", "Idioma", "ingles", "documentacion", "usuario", "español", "funciones", "clases", "comentarios", "excepciones", "Booleanos", "Prefijos", "descriptivos", "Eventos", "Getters", "Setters", "prefijos", "<PERSON><PERSON><PERSON>", "privados", "underscore", "Async", "segun", "lenguaje", "Tablas", "Columnas", "Indices", "tabla", "columna", "Variables", "Entorno", "Formato", "prefijos", "Agrupacion", "servicio", "funcionalidad", "Limites", "Estructura", "Archivos", "Limite", "Principal", "Lineas", "Justificacion", "Contexto", "optimo", "tokens", "<PERSON><PERSON><PERSON><PERSON>", "responsabilidad", "facil", "testing", "mejor", "review", "Excepciones", "Permitidas", "Criterios", "Configuracion", "limite", "config", "pura", "logica", "Técnico", "Información", "Descripción", "<PERSON><PERSON><PERSON><PERSON>", "administrativo", "moderno", "responsivo", "personalizable", "construido", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Está", "Componentes", "Autenticación", "Grá<PERSON><PERSON>", "comunes", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "específicos", "providers", "Custom", "hooks", "Iconos", "layout", "Utilidades", "estáticos", "idioma", "Configuraciones", "Importantes", "configuración", "loader", "aliases", "moderna", "Disponibles", "desarrollo", "producción", "código", "básica", "APIs", "externas", "servicios", "Adicional", "<PERSON><PERSON><PERSON>", "Arquitectura", "Implementados", "basada", "componentes", "manejo", "estado", "global", "Sidebar", "Theme", "complejos", "modulares", "reutilizables", "Lógica", "reutilizable", "encapsulada", "Características", "Especiales", "Proyecto", "routing", "layouts", "anidados", "servidor", "performance", "temas", "adaptativo", "extensa", "biblioteca", "Tipado", "completo", "transformados", "diferentes", "secciones", "Funcionales", "Incluidos", "m<PERSON><PERSON><PERSON>", "g<PERSON><PERSON><PERSON><PERSON>", "facturas", "transacciones", "Gestión", "relaciones", "clientes", "Generadores", "texto", "imagen", "video", "Formularios", "login", "registro", "recuperación", "archivos", "carpetas", "interactivo", "mensajería", "tickets", "soporte", "claves", "integraciones", "Documentación", "Existente", "básica", "instalación", "<PERSON><PERSON><PERSON><PERSON>", "Historial", "versiones", "línea", "vivo", "Configuración", "Especial", "reciente", "mejoras", "optimizado", "automática", "limpios", "Override", "compatibilidad", "Versión", "Actualización", "Licencia", "Comercial", "License", "Template", "Starter", "Premium", "requiere", "entorno", "funcionalidad", "básica", "Preparado", "para", "configuración", "Diseño", "Productos", "Calendario", "Sistema", "online", "<PERSON>", "<PERSON>", "ABOUT", "<PERSON><PERSON><PERSON><PERSON>", "superior", "recomendado", "Framework", "SSR", "SSG", "Biblioteca", "interfaz", "Wrapper", "Apex", "Charts", "visualizaciones", "<PERSON><PERSON><PERSON>", "slider", "Scrollbars", "personalizados", "Date", "picker", "Syntax", "highlighting", "Interacción", "Drag", "drop", "Backend", "HTML5", "File", "upload", "vectoriales", "mundial", "Utility", "condicionales", "<PERSON><PERSON>", "Tailwind", "Positioning", "engine", "Compilador", "<PERSON><PERSON>", "Tipos", "DOM", "PrismJS", "Webpack", "Procesador", "CSS", "utility", "first", "built", "bundler", "Transformación", "SVG", "React", "Procesamiento", "framework", "tests", "configurados", "Formatters", "legacy", "Router", "Grupo", "rutas", "admin", "<PERSON><PERSON><PERSON><PERSON>", "full", "width", "Layout", "raíz", "Estilos", "globales", "IA", "analytics", "UI", "base", "otros", "Context", "React", "Custom", "React", "hooks", "SVG", "Archivos", "Idiomas", "Next", "js", "TypeScript", "paths", "ESLint", "PostCSS", "Dependencias", "scripts", "proyecto", "<PERSON><PERSON><PERSON>", "Build", "Lin<PERSON>", "Variables", "Entorno", "No", "variables", "de", "entorno", "para", "funcionalidad", "básica", "Preparado", "para", "configuración", "de", "APIs", "externas", "y", "servicios", "Pro", "es", "un", "template", "de", "dashboard", "con", "Next", "js", "CSS", "desarrolladores", "crear", "dashboards", "funcionales", "atractivos", "manera", "<PERSON><PERSON><PERSON><PERSON>", "eficiente", "SPA", "Single", "Page", "Application", "App", "Router", "Server", "Components", "aplicaciones", "administrativas", "Multi", "system", "agrupadas", "Principal", "tipado", "está<PERSON><PERSON>", "Lenguaje", "principal", "compilación", "Runtime", "Entorno", "Framework", "biblioteca", "interfaz", "incluye", "base", "datos", "únicamente", "integración", "cualquier", "backend", "BD", "Principales", "Producción", "forms", "postcss", "autoprefixer", "Bibliotecas", "calendario", "Componente", "Grá<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Scrollbars", "Date", "picker", "Syntax", "highlighting", "Bibliotecas", "Interacción", "Drag", "drop", "Backend", "HTML5", "DnD", "File", "upload", "vectoriales", "Mapa", "mundial", "Utility", "clases", "condicionales", "<PERSON><PERSON>", "Positioning", "engine", "Dependencias", "desarrollo", "versiones", "Compilador", "<PERSON><PERSON>", "Tipos", "Node", "DOM", "PrismJS", "Loader", "SVG", "Webpack", "Procesador", "Herramientas", "build", "bundling", "built", "in", "bundler", "basado", "en", "Transformación", "a", "componentes", "React", "Procesamiento", "CSS", "utility", "first", "Framework", "CSS", "Herramientas", "de", "Testing", "No", "incluye", "testing", "framework", "Template", "base", "sin", "tests", "configurados", "Linters", "y", "Formatters", "ESLint", "9", "con", "configuración", "Next", "js", "eslintrc", "para", "compatibilidad", "con", "configuraciones", "legacy", "Diccionario", "t<PERSON><PERSON><PERSON><PERSON>", "técnicos", "utilizados", "Categorías", "incluidas", "Térm<PERSON>s", "generales", "desarrollo", "arquitectura", "específicos", "comunes", "técnica", "Configuración", "cSpell", "Archivos", "configuración", "principal", "específica", "Code", "Idiomas", "configurados", "Inglés", "código", "Español", "documentación", "Diccionarios", "activos", "personalizado", "español", "TypeScript", "Node", "js", "software", "general", "Overrides", "por", "tipo", "de", "archivo", "Archivos", "<PERSON><PERSON>", "md", "Idioma", "principal", "Español", "Diccionarios", "spanish", "software", "terms", "typescript", "node", "Archivos", "específicos", "ABOUT", "md", "con", "configuración", "especial", "Archivos", "de", "código", "ts", "tsx", "js", "jsx", "Idioma", "principal", "Inglés", "Diccionarios", "typescript", "node", "software", "terms", "Archivos", "de", "reglas", "augment", "rules", "md", "Idioma", "principal", "Español", "Diccionarios", "spanish", "software", "terms", "<PERSON><PERSON>", "Verificar", "configuración", "bash", "scripts", "cspell", "check", "js", "<PERSON><PERSON><PERSON>", "VS", "Code", "útiles", "Recargar", "configuración", "Ctrl", "Shift", "P", "cSpell", "Reload", "Configuration", "Agregar", "palabra", "Ctrl", "Shift", "P", "cSpell", "Add", "Words", "to", "Dictionary", "Verificar", "archivo", "Ctrl", "Shift", "P", "cSpell", "Check", "Document", "Agregar", "nuevas", "palabras", "<PERSON><PERSON><PERSON><PERSON>", "Automático", "desde", "VS", "Code", "<PERSON><PERSON>", "clic", "derecho", "en", "la", "palabra", "marcada", "como", "error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Add", "to", "dictionary", "Elegir", "el", "diccionario", "apropia<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Manual", "Abrir", "dictionaries", "spanish", "txt", "Agregar", "la", "palabra", "en", "la", "categoría", "apropiada", "Guardar", "el", "archivo", "Recargar", "configuración", "de", "cSpell", "Estructura", "del", "diccionario", "español", "text", "Térm<PERSON>s", "generales", "de", "documentación", "Técnico", "Información", "Descripción", "Térm<PERSON>s", "de", "desarrollo", "y", "arquitectura", "administrativo", "moderno", "responsivo", "Térm<PERSON>s", "técnicos", "específicos", "del", "proyecto", "Componentes", "Autenticación", "Grá<PERSON><PERSON>", "Térm<PERSON>s", "técnicos", "originales", "Reg<PERSON>", "Codigo", "Nomenclatura", "Solución", "de", "problemas", "Palabras", "marcadas", "como", "errores", "Verificar", "que", "el", "idioma", "esté", "configurado", "correctamente", "Agregar", "la", "palabra", "al", "diccionario", "apropia<PERSON>", "Recargar", "configuración", "de", "cSpell", "Configuración", "no", "se", "aplica", "Reiniciar", "VS", "Code", "Verificar", "que", "los", "archivos", "de", "configuración", "estén", "en", "la", "raíz", "del", "proyecto", "<PERSON><PERSON><PERSON><PERSON>", "el", "script", "de", "verificación", "<PERSON><PERSON><PERSON>", "positivos", "en", "<PERSON><PERSON><PERSON>", "Verificar", "que", "el", "archivo", "tenga", "la", "configuración", "de", "idioma", "correcta", "Los", "archivos", "de", "código", "deben", "usar", "<PERSON><PERSON><PERSON>", "por", "defecto", "Los", "archivos", "de", "documentación", "pueden", "usar", "español", "Mantenimiento", "Limpieza", "periódica", "Rev<PERSON><PERSON>", "palabras", "duplicadas", "en", "el", "diccionario", "Organizar", "palabras", "por", "categorías", "Eliminar", "palabras", "obsoletas", "Actualización", "Agregar", "nuevos", "t<PERSON><PERSON><PERSON><PERSON>", "técnicos", "<PERSON>g<PERSON>", "aparezcan", "<PERSON><PERSON><PERSON>", "consistencia", "en", "la", "nomenclatura", "Documentar", "cambios", "importantes", "<PERSON>a", "Esta", "configuración", "está", "optimizada", "para", "proyectos", "que", "mezclan", "documentación", "en", "español", "con", "código", "en", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "las", "mejores", "prácticas", "de", "desarrollo", "internacional"]}