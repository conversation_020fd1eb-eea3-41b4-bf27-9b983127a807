{"version": "0.2", "language": "en,es", "dictionaries": ["spanish", "typescript", "node", "software-terms"], "dictionaryDefinitions": [{"name": "spanish", "path": "./dictionaries/spanish.txt"}], "ignorePaths": ["node_modules/**", "dist/**", "build/**", ".git/**"], "overrides": [{"filename": "**/.augment/rules/*.md", "language": "es", "dictionaries": ["spanish", "software-terms"]}, {"filename": "**/ABOUT.md", "language": "es", "dictionaries": ["spanish", "software-terms", "typescript", "node"]}, {"filename": "**/*spanish*.txt", "language": "es", "dictionaries": ["spanish"]}], "words": ["Reg<PERSON>", "Codigo", "Nomenclatura", "Convenciones", "Idioma", "ingles", "documentacion", "usuario", "español", "funciones", "clases", "comentarios", "excepciones", "Booleanos", "Prefijos", "descriptivos", "Eventos", "Getters", "Setters", "prefijos", "<PERSON><PERSON><PERSON>", "privados", "underscore", "Async", "segun", "lenguaje", "Tablas", "Columnas", "Indices", "tabla", "columna", "Variables", "Entorno", "Formato", "prefijos", "Agrupacion", "servicio", "funcionalidad", "Limites", "Estructura", "Archivos", "Limite", "Principal", "Lineas", "Justificacion", "Contexto", "optimo", "tokens", "<PERSON><PERSON><PERSON><PERSON>", "responsabilidad", "facil", "testing", "mejor", "review", "Excepciones", "Permitidas", "Criterios", "Configuracion", "limite", "config", "pura", "logica", "Técnico", "Información", "Descripción", "<PERSON><PERSON><PERSON><PERSON>", "administrativo", "moderno", "responsivo", "personalizable", "construido", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Está", "Componentes", "Autenticación", "Grá<PERSON><PERSON>", "comunes", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "específicos", "providers", "Custom", "hooks", "Iconos", "layout", "Utilidades", "estáticos", "idioma", "Configuraciones", "Importantes", "configuración", "loader", "aliases", "moderna", "Disponibles", "desarrollo", "producción", "código", "básica", "APIs", "externas", "servicios", "Adicional", "<PERSON><PERSON><PERSON>", "Arquitectura", "Implementados", "basada", "componentes", "manejo", "estado", "global", "Sidebar", "Theme", "complejos", "modulares", "reutilizables", "Lógica", "reutilizable", "encapsulada", "Características", "Especiales", "Proyecto", "routing", "layouts", "anidados", "servidor", "performance", "temas", "adaptativo", "extensa", "biblioteca", "Tipado", "completo", "transformados", "diferentes", "secciones", "Funcionales", "Incluidos", "m<PERSON><PERSON><PERSON>", "g<PERSON><PERSON><PERSON><PERSON>", "facturas", "transacciones", "Gestión", "relaciones", "clientes", "Generadores", "texto", "imagen", "video", "Formularios", "login", "registro", "recuperación", "archivos", "carpetas", "interactivo", "mensajería", "tickets", "soporte", "claves", "integraciones", "Documentación", "Existente", "básica", "instalación", "<PERSON><PERSON><PERSON><PERSON>", "Historial", "versiones", "línea", "vivo", "Configuración", "Especial", "reciente", "mejoras", "optimizado", "automática", "limpios", "Override", "compatibilidad", "Versión", "Actualización", "Licencia", "Comercial", "License", "Template", "Starter", "Premium", "requiere", "entorno", "funcionalidad", "básica", "Preparado", "para", "configuración", "Diseño", "Productos", "Calendario", "Sistema", "online", "<PERSON>", "<PERSON>", "ABOUT", "<PERSON><PERSON><PERSON><PERSON>", "superior", "recomendado", "Framework", "SSR", "SSG", "Biblioteca", "interfaz", "Wrapper", "Apex", "Charts", "visualizaciones", "<PERSON><PERSON><PERSON>", "slider", "Scrollbars", "personalizados", "Date", "picker", "Syntax", "highlighting", "Interacción", "Drag", "drop", "Backend", "HTML5", "File", "upload", "vectoriales", "mundial", "Utility", "condicionales", "<PERSON><PERSON>", "Tailwind", "Positioning", "engine", "Compilador", "<PERSON><PERSON>", "Tipos", "DOM", "PrismJS", "Webpack", "Procesador", "CSS", "utility", "first", "built", "bundler", "Transformación", "SVG", "React", "Procesamiento", "framework", "tests", "configurados", "Formatters", "legacy", "Router", "Grupo", "rutas", "admin", "<PERSON><PERSON><PERSON><PERSON>", "full", "width", "Layout", "raíz", "Estilos", "globales", "IA", "analytics", "UI", "base", "otros", "Context", "React", "Custom", "React", "hooks", "SVG", "Archivos", "Idiomas", "Next", "js", "TypeScript", "paths", "ESLint", "PostCSS", "Dependencias", "scripts", "proyecto", "<PERSON><PERSON><PERSON>", "Build", "Lin<PERSON>", "Variables", "Entorno", "No", "variables", "de", "entorno", "para", "funcionalidad", "básica", "Preparado", "para", "configuración", "de", "APIs", "externas", "y", "servicios", "Pro", "es", "un", "template", "de", "dashboard", "con", "Next", "js", "CSS", "desarrolladores", "crear", "dashboards", "funcionales", "atractivos", "manera", "<PERSON><PERSON><PERSON><PERSON>", "eficiente", "SPA", "Single", "Page", "Application", "App", "Router", "Server", "Components", "aplicaciones", "administrativas", "Multi", "system", "agrupadas", "Principal", "tipado", "está<PERSON><PERSON>", "Lenguaje", "principal", "compilación", "Runtime", "Entorno", "Framework", "biblioteca", "interfaz", "incluye", "base", "datos", "únicamente", "integración", "cualquier", "backend", "BD", "Principales", "Producción", "forms", "postcss", "autoprefixer", "Bibliotecas", "calendario", "Componente", "Grá<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Scrollbars", "Date", "picker", "Syntax", "highlighting", "Bibliotecas", "Interacción", "Drag", "drop", "Backend", "HTML5", "DnD", "File", "upload", "vectoriales", "Mapa", "mundial", "Utility", "clases", "condicionales", "<PERSON><PERSON>", "Positioning", "engine", "Dependencias", "desarrollo", "versiones", "Compilador", "<PERSON><PERSON>", "Tipos", "Node", "DOM", "PrismJS", "Loader", "SVG", "Webpack", "Procesador", "Herramientas", "build", "bundling", "built", "in", "bundler", "basado", "en", "Transformación", "a", "componentes", "React", "Procesamiento", "CSS", "utility", "first", "Framework", "CSS", "Herramientas", "de", "Testing", "No", "incluye", "testing", "framework", "Template", "base", "sin", "tests", "configurados", "Linters", "y", "Formatters", "ESLint", "9", "con", "configuración", "Next", "js", "eslintrc", "para", "compatibilidad", "con", "configuraciones", "legacy"]}