{"version": "0.2", "language": "en,es", "dictionaries": ["spanish", "typescript", "node", "software-terms"], "dictionaryDefinitions": [{"name": "spanish", "path": "./dictionaries/spanish.txt"}], "ignorePaths": ["node_modules/**", "dist/**", "build/**", ".git/**"], "overrides": [{"filename": "**/.augment/rules/*.md", "language": "es", "dictionaries": ["spanish", "software-terms"]}], "words": ["Reg<PERSON>", "Codigo", "Nomenclatura", "Convenciones", "Idioma", "ingles", "documentacion", "usuario", "español", "funciones", "clases", "comentarios", "excepciones", "Booleanos", "Prefijos", "descriptivos", "Eventos", "Getters", "Setters", "prefijos", "<PERSON><PERSON><PERSON>", "privados", "underscore", "Async", "segun", "lenguaje", "Tablas", "Columnas", "Indices", "tabla", "columna", "Variables", "Entorno", "Formato", "prefijos", "Agrupacion", "servicio", "funcionalidad", "Limites", "Estructura", "Archivos", "Limite", "Principal", "Lineas", "Justificacion", "Contexto", "optimo", "tokens", "<PERSON><PERSON><PERSON><PERSON>", "responsabilidad", "facil", "testing", "mejor", "review", "Excepciones", "Permitidas", "Criterios", "Configuracion", "limite", "config", "pura", "logica"]}