---
type: "always_apply"
---

# Reglas de Git

## Estrategia Branching

### Branches Principales
- **main**: Produccion (codigo estable, deployable)
- **develop**: Integracion de desarrollo
- **feature/T-XXX-description**: Nuevas funcionalidades
- **bugfix/T-XXX-description**: Correccion bugs
- **hotfix/T-XXX-description**: Fixes urgentes produccion

### Flujo Individual
1. **Iniciar**: Actualizar develop, crear branch feature
2. **Desarrollar**: Commits atomicos regulares
3. **Integrar**: Rebase con develop, push a remoto
4. **Finalizar**: Merge no-fast-forward, limpiar branch

## Formato Commits Simplificado

### Formato Basico
- **Estructura**: `[tipo] T-XXX: descripcion breve`
- **Ejemplo**: `[feat] T-001: Add user authentication`

### Tipos Commits
- `[feat]`: Nueva funcionalidad
- `[fix]`: Correccion bug
- `[docs]`: Documentacion
- `[refactor]`: Refactoring codigo
- `[test]`: Tests
- `[chore]`: Mantenimiento

### Triggers Automaticos
1. Cada 2 horas trabajo activo
2. Completar tarea (T-XXX)
3. Antes cambiar contexto
4. Finalizar jornada

### Buenas Descripciones
- **Correctas**: "Add OAuth integration", "Fix validation error"
- **Evitar**: "Fix bug", "Updates", "WIP"

## Colaboracion Multi-Desarrollador

### Resolucion Conflictos
1. **Comunicacion**: Avisar antes trabajar archivos compartidos
2. **Frecuencia**: Pull/rebase minimo 2 veces dia
3. **Conflictos**: Resolver inmediatamente, no postponer
4. **Validacion**: Test despues resolver conflicto

### Proceso Conflictos
```bash
# 1. Identificar conflictos
git status

# 2. Resolver archivos uno por uno
# Buscar <<<<<<< ======= >>>>>>>
# Elegir version correcta o combinar

# 3. Probar la solucion
npm test

# 4. Finalizar
git add .
git rebase --continue
```

### Estrategias por Escenario
- **Misma feature, diferentes devs**: Comunicar cambios, micro-commits
- **Features relacionadas**: Merge develop frecuente
- **Codigo base compartido**: Atomic commits, tests obligatorios
- **Conflictos complejos**: Pair programming para resolver

## Automatizacion

### Git Hooks Esenciales
- **Pre-commit**: Linter, tests basicos
- **Post-commit**: Actualizar gitlog.md
- **Pre-push**: Tests integracion

### Release Process
1. Actualizar CHANGELOG.md
2. Tag version (v1.2.0)
3. Push cambios y tags

## Configuracion

### Setup Basico
- **user.name/email**: Identidad commits
- **core.autocrlf**: true (Windows)
- **init.defaultBranch**: main

### .gitignore Esencial
```
node_modules/
.env*
dist/
*.log
gitlog.md
```

## Documentacion Cambios

### CHANGELOG.md (Publico)
- **Formato**: Keep a Changelog
- **Secciones**: Added, Fixed, Changed, Security
- **Version**: [1.2.0] - 2024-01-15

### gitlog.md (Privado)
- **Auto-generado**: Via post-commit hook
- **No commitear**: En .gitignore
- **Formato**: `- [tipo] T-XXX: descripcion`

## Comandos Rapidos

### Uso Diario
```bash
# Iniciar feature
git checkout develop && git pull
git checkout -b feature/T-123-new-login

# Commits regulares
git add . && git commit -m "[feat] T-123: Add login form"

# Integrar cambios
git checkout develop && git pull
git checkout feature/T-123-new-login
git rebase develop

# Finalizar
git checkout develop
git merge --no-ff feature/T-123-new-login
git branch -d feature/T-123-new-login
```

### Resolucion Conflictos
```bash
# Ver conflictos
git status

# Despues resolver manualmente
git add archivo-resuelto.js
git rebase --continue

# Si es muy complejo
git rebase --abort  # empezar de nuevo
```

## Mejores Practicas

### Commits
- Cambios atomicos y logicos
- Descripcion clara proposito
- Tests passing antes commit
- No codigo comentado o debug

### Colaboracion
- Pull antes push siempre
- Comunicar cambios grandes
- Resolver conflictos rapidamente
- Branches cortas (<1 semana)

### Integracion Augment Code
- Auto-commit cada 2h con formato
- Branch names incluyen task-id
- Tracking estadisticas por tarea
- CHANGELOG auto-actualizado