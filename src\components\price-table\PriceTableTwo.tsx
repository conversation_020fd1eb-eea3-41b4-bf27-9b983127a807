import React from "react";
import { CheckLineIcon, CloseLineIcon } from "../../icons";

const personalPack = [
  { name: "5 website", included: true },
  { name: "500 MB Storage", included: true },
  { name: "Unlimited Sub-Domain", included: true },
  { name: "3 Custom Domain", included: true },
  { name: "Free SSL Certificate", included: false },
  { name: "Unlimited Traffic", included: false },
];
const professionalPack = [
  { name: "10 website", included: true },
  { name: "1GB Storage", included: true },
  { name: "Unlimited Sub-Domain", included: true },
  { name: "5 Custom Domain", included: true },
  { name: "Free SSL Certificate", included: true },
  { name: "Unlimited Traffic", included: false },
];
const enterprisePack = [
  { name: "15 website", included: true },
  { name: "10GB Storage", included: true },
  { name: "Unlimited Sub-Domain", included: true },
  { name: "10 Custom Domain", included: true },
  { name: "Free SSL Certificate", included: true },
  { name: "Unlimited Traffic", included: true },
];

export default function PriceTableTwo() {
  return (
    <div className="grid gap-5 gird-cols-1 sm:grid-cols-2 xl:grid-cols-3 xl:gap-6">
      {/* <!-- Pricing item --> */}
      <div className="rounded-2xl border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-white/[0.03] xl:p-8">
        <div className="flex items-start justify-between -mb-4">
          <span className="block font-semibold text-gray-800 text-theme-xl dark:text-white/90">
            Personal
          </span>

          <span className="flex h-[56px] w-[56px] items-center justify-center rounded-[10.5px] bg-brand-50 text-brand-500">
            <svg
              className="fill-current"
              width="29"
              height="28"
              viewBox="0 0 29 28"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M11.4072 8.64984C11.4072 6.77971 12.9232 5.26367 14.7934 5.26367C16.6635 5.26367 18.1795 6.77971 18.1795 8.64984C18.1795 10.52 16.6635 12.036 14.7934 12.036C12.9232 12.036 11.4072 10.52 11.4072 8.64984ZM14.7934 3.48633C11.9416 3.48633 9.62986 5.79811 9.62986 8.64984C9.62986 11.5016 11.9416 13.8133 14.7934 13.8133C17.6451 13.8133 19.9569 11.5016 19.9569 8.64984C19.9569 5.79811 17.6451 3.48633 14.7934 3.48633ZM12.8251 15.6037C8.49586 15.6037 4.98632 19.1133 4.98632 23.4425V23.847C4.98632 24.3378 5.38419 24.7357 5.87499 24.7357C6.36579 24.7357 6.76366 24.3378 6.76366 23.847V23.4425C6.76366 20.0949 9.47746 17.3811 12.8251 17.3811H16.7635C20.1111 17.3811 22.8249 20.0949 22.8249 23.4425V23.847C22.8249 24.3378 23.2228 24.7357 23.7136 24.7357C24.2044 24.7357 24.6023 24.3378 24.6023 23.847V23.4425C24.6023 19.1133 21.0927 15.6037 16.7635 15.6037H12.8251Z"
                fill=""
              />
            </svg>
          </span>
        </div>

        <div className="flex items-end">
          <h2 className="font-bold text-gray-800 text-title-md dark:text-white/90">
            $59.00
          </h2>

          <span className="inline-block mb-1 text-sm text-gray-500 dark:text-gray-400">
            / Lifetime
          </span>
        </div>

        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          For solo designers & freelancers
        </p>

        <div className="w-full h-px my-6 bg-gray-200 dark:bg-gray-800"></div>

        <ul className="mb-8 space-y-3">
          {personalPack.map((item, index) => (
            <li
              key={index}
              className={`flex items-center gap-3 text-sm  ${
                item.included
                  ? "text-gray-700 dark:text-gray-400"
                  : "text-gray-400"
              }`}
            >
              {item.included ? (
                <CheckLineIcon className="text-success-500" />
              ) : (
                <CloseLineIcon className="text-gray-400" />
              )}
              {item.name}
            </li>
          ))}
        </ul>

        <button className="flex w-full items-center justify-center rounded-lg bg-gray-800 p-3.5 text-sm font-medium text-white shadow-theme-xs transition-colors hover:bg-brand-500 dark:bg-white/10">
          Choose Starter
        </button>
      </div>

      {/* <!-- Pricing item --> */}
      <div className="rounded-2xl border-2 border-brand-500 bg-white p-6 dark:border-brand-500 dark:bg-white/[0.03] xl:p-8">
        <div className="flex items-start justify-between -mb-4">
          <span className="block font-semibold text-gray-800 text-theme-xl dark:text-white/90">
            Professional
          </span>

          <span className="flex h-[56px] w-[56px] items-center justify-center rounded-[10.5px] bg-brand-50 text-brand-500">
            <svg
              className="fill-current"
              width="29"
              height="28"
              viewBox="0 0 29 28"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M12.2969 3.55469C10.8245 3.55469 9.6309 4.7483 9.6309 6.2207V7.10938H6.29462C4.82222 7.10938 3.6286 8.30299 3.6286 9.77539V20.4395C3.6286 21.9119 4.82222 23.1055 6.29462 23.1055H23.4758C24.9482 23.1055 26.1419 21.9119 26.1419 20.4395V9.77539C26.1419 8.30299 24.9482 7.10938 23.4758 7.10938H19.7025V6.2207C19.7025 4.7483 18.5089 3.55469 17.0365 3.55469H12.2969ZM18.8148 8.88672C18.8145 8.88672 18.8142 8.88672 18.8138 8.88672H10.5196C10.5193 8.88672 10.5189 8.88672 10.5186 8.88672H6.29462C5.80382 8.88672 5.40595 9.28459 5.40595 9.77539V10.9666L14.5355 14.8792C14.759 14.975 15.012 14.975 15.2356 14.8792L24.3645 10.9669V9.77539C24.3645 9.28459 23.9666 8.88672 23.4758 8.88672H18.8148ZM17.9252 7.10938V6.2207C17.9252 5.7299 17.5273 5.33203 17.0365 5.33203H12.2969C11.8061 5.33203 11.4082 5.7299 11.4082 6.2207V7.10938H17.9252ZM5.40595 20.4395V12.9003L13.8353 16.5129C14.506 16.8003 15.2651 16.8003 15.9357 16.5129L24.3645 12.9006V20.4395C24.3645 20.9303 23.9666 21.3281 23.4758 21.3281H6.29462C5.80382 21.3281 5.40595 20.9303 5.40595 20.4395Z"
                fill=""
              />
            </svg>
          </span>
        </div>

        <div className="flex items-end">
          <h2 className="font-bold text-gray-800 text-title-md dark:text-white/90">
            $199.00
          </h2>

          <span className="inline-block mb-1 text-sm text-gray-500 dark:text-gray-400">
            / Lifetime
          </span>
        </div>

        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          For working on commercial projects
        </p>

        <div className="w-full h-px my-6 bg-white/20"></div>

        <ul className="mb-8 space-y-3">
          {professionalPack.map((item, index) => (
            <li
              key={index}
              className={`flex items-center gap-3 text-sm  ${
                item.included
                  ? "text-gray-700 dark:text-gray-400"
                  : "text-gray-400"
              }`}
            >
              {item.included ? (
                <CheckLineIcon className="text-success-500" />
              ) : (
                <CloseLineIcon className="text-gray-400" />
              )}
              {item.name}
            </li>
          ))}
        </ul>

        <button className="flex w-full items-center justify-center rounded-lg bg-brand-500 p-3.5 text-sm font-medium text-white shadow-theme-xs hover:bg-brand-600">
          Choose This Plan
        </button>
      </div>

      {/* <!-- Pricing item --> */}
      <div className="rounded-2xl border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-white/[0.03] xl:p-8">
        <div className="flex items-start justify-between -mb-4">
          <span className="block font-semibold text-gray-800 text-theme-xl dark:text-white/90">
            Enterprise
          </span>

          <span className="flex h-[56px] w-[56px] items-center justify-center rounded-[10.5px] bg-brand-50 text-brand-500">
            <svg
              className="fill-current"
              width="28"
              height="28"
              viewBox="0 0 28 28"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M23.7507 1.28757C24.0978 0.940553 24.6605 0.940611 25.0075 1.28769C25.3545 1.63478 25.3544 2.19745 25.0074 2.54447L19.8787 7.67208C19.5316 8.0191 18.9689 8.01904 18.6219 7.67195C18.2749 7.32487 18.275 6.76219 18.622 6.41518L23.7507 1.28757ZM19.4452 3.1553C19.7922 2.80822 19.7921 2.24554 19.4451 1.89853C19.098 1.55151 18.5353 1.55157 18.1883 1.89866L16.4386 3.64866C16.0916 3.99574 16.0917 4.55842 16.4388 4.90543C16.7859 5.25244 17.3485 5.25238 17.6955 4.9053L19.4452 3.1553ZM13.8188 4.02442C13.6691 3.72109 13.3602 3.52905 13.0219 3.52905C12.6837 3.52905 12.3747 3.72109 12.225 4.02442L9.39921 9.75015L3.08049 10.6683C2.74574 10.717 2.46763 10.9514 2.3631 11.2731C2.25857 11.5948 2.34575 11.948 2.58797 12.1841L7.16024 16.641L6.08087 22.9342C6.02369 23.2676 6.16075 23.6045 6.43441 23.8033C6.70807 24.0022 7.07088 24.0284 7.37029 23.871L13.0219 20.8997L18.6736 23.871C18.973 24.0284 19.3358 24.0022 19.6094 23.8033C19.8831 23.6045 20.0202 23.2676 19.963 22.9342L18.8836 16.641L23.4559 12.1841C23.6981 11.948 23.7853 11.5948 23.6807 11.2731C23.5762 10.9514 23.2981 10.717 22.9634 10.6683L16.6446 9.75015L13.8188 4.02442ZM10.7862 10.9557L13.0219 6.42572L15.2576 10.9557C15.387 11.218 15.6373 11.3998 15.9267 11.4418L20.9258 12.1683L17.3084 15.6944C17.099 15.8985 17.0034 16.1927 17.0529 16.4809L17.9068 21.4599L13.4355 19.1091C13.1766 18.973 12.8673 18.973 12.6084 19.1091L8.13703 21.4599L8.99098 16.4809C9.04043 16.1927 8.94485 15.8985 8.7354 15.6944L5.118 12.1683L10.1171 11.4418C10.4066 11.3998 10.6568 11.218 10.7862 10.9557ZM25.2694 5.97276C25.6165 6.31978 25.6166 6.88245 25.2696 7.22954L23.5199 8.97954C23.1729 9.32662 22.6102 9.32668 22.2632 8.97967C21.9161 8.63265 21.916 8.06998 22.263 7.72289L24.0127 5.97289C24.3597 5.62581 24.9224 5.62575 25.2694 5.97276Z"
                fill=""
              />
            </svg>
          </span>
        </div>

        <div className="flex items-end">
          <h2 className="font-bold text-gray-800 text-title-md dark:text-white/90">
            $599.00
          </h2>

          <span className="inline-block mb-1 text-sm text-gray-500 dark:text-gray-400">
            / Lifetime
          </span>
        </div>

        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          For teams larger than 5 members
        </p>

        <div className="w-full h-px my-6 bg-gray-200 dark:bg-gray-800"></div>

        <ul className="mb-8 space-y-3">
          <ul className="mb-8 space-y-3">
            {enterprisePack.map((item, index) => (
              <li
                key={index}
                className={`flex items-center gap-3 text-sm  ${
                  item.included
                    ? "text-gray-700 dark:text-gray-400"
                    : "text-gray-400"
                }`}
              >
                {item.included ? (
                  <CheckLineIcon className="text-success-500" />
                ) : (
                  <CloseLineIcon className="text-gray-400" />
                )}
                {item.name}
              </li>
            ))}
          </ul>
        </ul>

        <button className="flex w-full items-center justify-center rounded-lg bg-gray-800 p-3.5 text-sm font-medium text-white shadow-theme-xs transition-colors hover:bg-brand-500 dark:bg-white/10">
          Choose This Plan
        </button>
      </div>
    </div>
  );
}
