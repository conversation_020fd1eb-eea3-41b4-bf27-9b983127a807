{"name": "tailadmin-next-typescript-pro", "version": "2.2.0", "private": true, "scripts": {"dev": "next dev ", "build": "next build ", "start": "next start", "lint": "next lint"}, "dependencies": {"@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@popperjs/core": "^2.11.8", "@react-jvectormap/core": "^1.0.4", "@react-jvectormap/world": "^1.1.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/postcss": "^4.0.9", "apexcharts": "^4.3.0", "autoprefixer": "^10.4.20", "clsx": "^2.1.1", "flatpickr": "^4.6.13", "next": "^15.4.3", "prismjs": "^1.30.0", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "simplebar-react": "^3.3.0", "swiper": "^11.2.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@types/node": "^20", "@types/prismjs": "^1.26.5", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.3", "postcss": "^8", "typescript": "^5"}, "overrides": {"@react-jvectormap/core": {"react": "^16.8.0 || ^17 || ^18 || ^19", "react-dom": "^16.8.0 || ^17 || ^18 || ^19"}, "@react-jvectormap/world": {"react": "^16.8.0 || ^17 || ^18 || ^19", "react-dom": "^16.8.0 || ^17 || ^18 || ^19"}}}