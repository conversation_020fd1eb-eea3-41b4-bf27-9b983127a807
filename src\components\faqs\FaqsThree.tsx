import React from "react";

export default function FaqsThree() {
  return (
    <div className="grid gird-cols-1 gap-x-8 xl:grid-cols-2">
      <div className="space-y-3 sm:space-y-5">
        {/* <!-- item--> */}
        <div className="py-4">
          <div className="flex items-start gap-4">
            <div className="text-gray-700 dark:text-gray-500">
              <svg
                className="fill-current"
                width="24"
                height="26"
                viewBox="0 0 24 26"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M3.75 14C3.75 9.44365 7.44365 5.75 12 5.75C16.5563 5.75 20.25 9.44365 20.25 14C20.25 18.5563 16.5563 22.25 12 22.25C7.44365 22.25 3.75 18.5563 3.75 14ZM12 3.75C6.33908 3.75 1.75 8.33908 1.75 14C1.75 19.6609 6.33908 24.25 12 24.25C17.6609 24.25 22.25 19.6609 22.25 14C22.25 8.33908 17.6609 3.75 12 3.75ZM10.7491 9.52507C10.7491 10.2154 11.3088 10.7751 11.9991 10.7751H12.0001C12.6905 10.7751 13.2501 10.2154 13.2501 9.52507C13.2501 8.83472 12.6905 8.27507 12.0001 8.27507H11.9991C11.3088 8.27507 10.7491 8.83472 10.7491 9.52507ZM12.0001 19.6214C11.4478 19.6214 11.0001 19.1737 11.0001 18.6214V12.9449C11.0001 12.3926 11.4478 11.9449 12.0001 11.9449C12.5524 11.9449 13.0001 12.3926 13.0001 12.9449V18.6214C13.0001 19.1737 12.5524 19.6214 12.0001 19.6214Z"
                  fill=""
                />
              </svg>
            </div>

            <div>
              <h4 className="mb-3 text-lg font-medium text-gray-800 dark:text-white/90">
                Do I get free updates?
              </h4>

              <p className="text-base text-gray-500 dark:text-gray-400">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                Praesent et nunc ut risus imperdiet lacinia.
                <br />
                <br />
                Lorem ipsum dolor sit amet, consectetur adipiscing elit.
              </p>
            </div>
          </div>
        </div>

        {/* <!-- divider --> */}
        <div className="w-full h-px bg-gray-200 dark:bg-gray-800"></div>

        {/* <!-- item--> */}
        <div className="py-4">
          <div className="flex items-start gap-4">
            <div className="text-gray-700 dark:text-gray-500">
              <svg
                className="fill-current"
                width="24"
                height="26"
                viewBox="0 0 24 26"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M3.75 14C3.75 9.44365 7.44365 5.75 12 5.75C16.5563 5.75 20.25 9.44365 20.25 14C20.25 18.5563 16.5563 22.25 12 22.25C7.44365 22.25 3.75 18.5563 3.75 14ZM12 3.75C6.33908 3.75 1.75 8.33908 1.75 14C1.75 19.6609 6.33908 24.25 12 24.25C17.6609 24.25 22.25 19.6609 22.25 14C22.25 8.33908 17.6609 3.75 12 3.75ZM10.7491 9.52507C10.7491 10.2154 11.3088 10.7751 11.9991 10.7751H12.0001C12.6905 10.7751 13.2501 10.2154 13.2501 9.52507C13.2501 8.83472 12.6905 8.27507 12.0001 8.27507H11.9991C11.3088 8.27507 10.7491 8.83472 10.7491 9.52507ZM12.0001 19.6214C11.4478 19.6214 11.0001 19.1737 11.0001 18.6214V12.9449C11.0001 12.3926 11.4478 11.9449 12.0001 11.9449C12.5524 11.9449 13.0001 12.3926 13.0001 12.9449V18.6214C13.0001 19.1737 12.5524 19.6214 12.0001 19.6214Z"
                  fill=""
                />
              </svg>
            </div>

            <div>
              <h4 className="mb-3 text-lg font-medium text-gray-800 dark:text-white/90">
                Which license type is suitable for me?
              </h4>

              <p className="text-base text-gray-500 dark:text-gray-400">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit.
              </p>
            </div>
          </div>
        </div>

        {/* <!-- divider --> */}
        <div className="w-full h-px bg-gray-200 dark:bg-gray-800"></div>

        {/* <!-- item--> */}
        <div className="py-4">
          <div className="flex items-start gap-4">
            <div className="text-gray-700 dark:text-gray-500">
              <svg
                className="fill-current"
                width="24"
                height="26"
                viewBox="0 0 24 26"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M3.75 14C3.75 9.44365 7.44365 5.75 12 5.75C16.5563 5.75 20.25 9.44365 20.25 14C20.25 18.5563 16.5563 22.25 12 22.25C7.44365 22.25 3.75 18.5563 3.75 14ZM12 3.75C6.33908 3.75 1.75 8.33908 1.75 14C1.75 19.6609 6.33908 24.25 12 24.25C17.6609 24.25 22.25 19.6609 22.25 14C22.25 8.33908 17.6609 3.75 12 3.75ZM10.7491 9.52507C10.7491 10.2154 11.3088 10.7751 11.9991 10.7751H12.0001C12.6905 10.7751 13.2501 10.2154 13.2501 9.52507C13.2501 8.83472 12.6905 8.27507 12.0001 8.27507H11.9991C11.3088 8.27507 10.7491 8.83472 10.7491 9.52507ZM12.0001 19.6214C11.4478 19.6214 11.0001 19.1737 11.0001 18.6214V12.9449C11.0001 12.3926 11.4478 11.9449 12.0001 11.9449C12.5524 11.9449 13.0001 12.3926 13.0001 12.9449V18.6214C13.0001 19.1737 12.5524 19.6214 12.0001 19.6214Z"
                  fill=""
                />
              </svg>
            </div>

            <div>
              <h4 className="mb-3 text-lg font-medium text-gray-800 dark:text-white/90">
                What are the &quot;Seats&quot; mentioned on pricing plans?
              </h4>

              <p className="text-base text-gray-500 dark:text-gray-400">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                Praesent et nunc ut risus imperdiet lacinia.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-3 sm:space-y-5">
        {/* <!-- item--> */}
        <div className="py-4">
          <div className="flex items-start gap-4">
            <div className="text-gray-700 dark:text-gray-500">
              <svg
                className="fill-current"
                width="24"
                height="26"
                viewBox="0 0 24 26"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M3.75 14C3.75 9.44365 7.44365 5.75 12 5.75C16.5563 5.75 20.25 9.44365 20.25 14C20.25 18.5563 16.5563 22.25 12 22.25C7.44365 22.25 3.75 18.5563 3.75 14ZM12 3.75C6.33908 3.75 1.75 8.33908 1.75 14C1.75 19.6609 6.33908 24.25 12 24.25C17.6609 24.25 22.25 19.6609 22.25 14C22.25 8.33908 17.6609 3.75 12 3.75ZM10.7491 9.52507C10.7491 10.2154 11.3088 10.7751 11.9991 10.7751H12.0001C12.6905 10.7751 13.2501 10.2154 13.2501 9.52507C13.2501 8.83472 12.6905 8.27507 12.0001 8.27507H11.9991C11.3088 8.27507 10.7491 8.83472 10.7491 9.52507ZM12.0001 19.6214C11.4478 19.6214 11.0001 19.1737 11.0001 18.6214V12.9449C11.0001 12.3926 11.4478 11.9449 12.0001 11.9449C12.5524 11.9449 13.0001 12.3926 13.0001 12.9449V18.6214C13.0001 19.1737 12.5524 19.6214 12.0001 19.6214Z"
                  fill=""
                />
              </svg>
            </div>

            <div>
              <h4 className="mb-3 text-lg font-medium text-gray-800 dark:text-white/90">
                Can I Customize TailAdmin to suit my needs?
              </h4>

              <p className="text-base text-gray-500 dark:text-gray-400">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                Praesent et nunc ut risus imperdiet lacinia.
                <br />
                <br />
                Lorem ipsum dolor sit amet, consectetur adipiscing elit.
              </p>
            </div>
          </div>
        </div>

        {/* <!-- divider --> */}
        <div className="w-full h-px bg-gray-200 dark:bg-gray-800"></div>

        {/* <!-- item--> */}
        <div className="py-4">
          <div className="flex items-start gap-4">
            <div className="text-gray-700 dark:text-gray-500">
              <svg
                className="fill-current"
                width="24"
                height="26"
                viewBox="0 0 24 26"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M3.75 14C3.75 9.44365 7.44365 5.75 12 5.75C16.5563 5.75 20.25 9.44365 20.25 14C20.25 18.5563 16.5563 22.25 12 22.25C7.44365 22.25 3.75 18.5563 3.75 14ZM12 3.75C6.33908 3.75 1.75 8.33908 1.75 14C1.75 19.6609 6.33908 24.25 12 24.25C17.6609 24.25 22.25 19.6609 22.25 14C22.25 8.33908 17.6609 3.75 12 3.75ZM10.7491 9.52507C10.7491 10.2154 11.3088 10.7751 11.9991 10.7751H12.0001C12.6905 10.7751 13.2501 10.2154 13.2501 9.52507C13.2501 8.83472 12.6905 8.27507 12.0001 8.27507H11.9991C11.3088 8.27507 10.7491 8.83472 10.7491 9.52507ZM12.0001 19.6214C11.4478 19.6214 11.0001 19.1737 11.0001 18.6214V12.9449C11.0001 12.3926 11.4478 11.9449 12.0001 11.9449C12.5524 11.9449 13.0001 12.3926 13.0001 12.9449V18.6214C13.0001 19.1737 12.5524 19.6214 12.0001 19.6214Z"
                  fill=""
                />
              </svg>
            </div>

            <div>
              <h4 className="mb-3 text-lg font-medium text-gray-800 dark:text-white/90">
                What does &quot;Unlimited Projects&quot; mean?
              </h4>

              <p className="text-base text-gray-500 dark:text-gray-400">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec
                quis magna ac nibh malesuada consectetur at vitae ipsum. Lorem
                ipsum dolor sit amet, consectetur adipiscing elit. Nam
                fermentum, leo et lacinia accumsan, ligula ante hendrerit nisi,
                eget vulputate ante justo et justo.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
