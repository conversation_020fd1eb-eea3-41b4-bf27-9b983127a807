function MailchimpIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
    >
      <path
        d="M12.321 24.9775C11.9035 24.3455 12.9535 23.389 12.5264 22.5859C12.2802 22.1227 11.8779 21.8341 11.3946 21.7735C10.9308 21.7153 10.4531 21.8851 10.1486 22.2169C9.66775 22.7401 9.59299 23.4523 9.68599 23.7043C9.72002 23.7967 9.77351 23.8219 9.81181 23.8273C10.0737 23.8618 10.221 23.1897 10.3115 23.0497C10.5614 22.6639 11.129 22.5471 11.5211 22.8007C12.2643 23.2814 11.6198 24.0584 11.6754 24.7147C11.7295 25.3537 12.1277 25.6105 12.4851 25.6375C12.8328 25.6507 13.0759 25.4575 13.1373 25.3165C13.2845 24.9799 12.6644 25.4975 12.321 24.9775Z"
        fill="#0C111D"
      />
      <path
        d="M27.8512 19.1761C27.6563 19.1492 27.4432 19.1498 27.2215 19.1761C27.0532 18.9637 26.9024 18.6199 26.8173 18.2185C26.6659 17.5045 26.6817 16.9873 27.1042 16.9201C27.5266 16.8529 27.7309 17.2849 27.8822 17.9989C27.9837 18.4789 27.9643 18.9199 27.8512 19.1761Z"
        fill="#0C111D"
      />
      <path
        d="M23.2243 19.6009C23.241 19.7626 23.2469 19.9262 23.2422 20.0769C23.6509 20.1009 23.9405 20.295 24.0175 20.4182C24.0571 20.4818 24.0412 20.5232 24.0285 20.5424C23.9862 20.6082 23.8955 20.598 23.7055 20.5767C23.5396 20.5581 23.3609 20.5417 23.1752 20.5503C23.0742 20.8602 22.7745 20.8892 22.564 20.66C22.417 20.7048 22.1281 20.8892 22.0421 20.6888C22.0413 20.5893 22.1453 20.4447 22.3337 20.317C22.2051 20.0718 22.1189 19.8092 22.068 19.5373C21.801 19.5854 21.5607 19.6602 21.3725 19.7188C21.284 19.7464 20.9343 19.9044 20.8933 19.7263C20.8659 19.6033 21.0574 19.4005 21.2598 19.2541C21.486 19.0935 21.7369 18.9794 21.997 18.9135C21.9914 18.5263 22.0902 18.2561 22.3716 18.2113C22.7204 18.1558 22.9366 18.4244 23.0863 18.9218C23.5079 19.0389 23.9289 19.3294 24.1148 19.6297C24.1871 19.7461 24.2011 19.8361 24.1543 19.8835C24.0375 20.0046 23.3901 19.6535 23.2243 19.6009Z"
        fill="#0C111D"
      />
      <path
        d="M25.9967 21.3415C26.2641 21.4711 26.5583 21.4201 26.6544 21.2275C26.7504 21.0349 26.6112 20.7739 26.3438 20.6443C26.0763 20.5147 25.7821 20.5657 25.6861 20.7583C25.5901 20.9509 25.7292 21.2119 25.9967 21.3415Z"
        fill="#0C111D"
      />
      <path
        d="M27.3132 20.3851C27.3181 20.0905 27.4986 19.8553 27.7156 19.8589C27.9326 19.8631 28.1046 20.1043 28.0998 20.3983C28.0949 20.6923 27.9144 20.9275 27.6974 20.9239C27.4804 20.9203 27.3084 20.6791 27.3132 20.3851Z"
        fill="#0C111D"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M33.1978 23.9929C33.196 23.9868 33.202 24.0079 33.1978 23.9929C33.6336 23.9929 34.318 24.4873 34.318 25.6819C34.318 26.8705 33.8184 28.2169 33.7004 28.5157C31.9006 32.7733 27.605 35.1433 22.4882 34.9933C17.7179 34.8535 13.6497 32.3689 11.8693 28.3183C10.7928 28.3195 9.68291 27.8521 8.83923 27.1141C7.94996 26.3365 7.40169 25.3303 7.29471 24.2809C7.21144 23.4637 7.31294 22.7035 7.57249 22.0405L6.5726 21.2041C1.99679 17.3906 16.3084 1.68861 20.8854 5.6294C20.9085 5.6492 22.4427 7.13479 22.4463 7.13839C24.6532 6.21344 30.5665 4.45236 30.5731 8.55199C30.5755 9.91338 29.6966 11.501 28.2882 12.9416C29.9158 14.4315 29.4867 16.5479 29.7458 18.4897L30.3111 18.6445C31.3888 18.9427 32.1553 19.3405 32.5303 19.7293C32.9054 20.1175 33.0914 20.4937 33.1589 20.9347C33.2221 21.2905 33.2136 21.9187 32.7382 22.6213C32.9094 23.0746 33.0656 23.5264 33.1978 23.9929ZM11.7885 27.2137C11.9483 27.2173 12.107 27.2071 12.2626 27.1807C13.9347 26.8993 14.3724 25.1065 14.097 23.3461C13.7858 21.3583 12.4237 20.6575 11.4991 20.6077C11.242 20.5945 11.0031 20.6173 10.8062 20.6563C9.15591 20.9845 8.22409 22.3705 8.40766 24.1705C8.5736 25.7995 10.2427 27.1729 11.7885 27.2137ZM8.05086 21.1447C8.6046 20.3509 9.50967 19.7767 10.5369 19.5793C11.8444 16.115 14.0277 12.923 16.9174 10.7264C19.0619 8.96058 21.3747 7.69399 21.3747 7.69399C21.3747 7.69399 20.1292 6.26839 19.753 6.16339C17.4389 5.5466 12.4413 8.94858 9.25013 13.4438C7.95908 15.2624 6.11064 18.4831 6.99444 20.1397C7.10324 20.3449 7.7202 20.8723 8.05086 21.1447ZM26.8118 26.4181C26.8154 26.4553 26.7924 26.4937 26.7589 26.5075C26.7589 26.5075 24.8995 27.3613 21.9461 26.4595C22.0523 27.3448 23.1259 27.6803 23.8759 27.8017C27.5357 28.4233 30.9573 26.3575 31.7274 25.8373C31.8596 25.748 31.7258 25.9776 31.7019 26.0113C30.7591 27.2119 28.2244 28.6021 24.9269 28.6015C23.4888 28.6009 22.0512 28.1011 21.5236 27.3325C20.7048 26.1403 21.4829 24.3997 22.8475 24.5809C25.1541 24.838 27.5188 24.6448 29.6541 23.6665C31.5159 22.8133 32.2191 21.8749 32.1134 21.1147C31.9493 19.9366 30.2026 19.7524 29.3197 19.4659C28.9356 19.3405 28.7459 19.2403 28.7028 18.5269C28.6839 18.2155 28.6292 17.129 28.6092 16.6796C28.5739 15.893 28.4779 14.8172 27.802 14.3732C27.6257 14.2574 27.43 14.2016 27.2239 14.1908C27.0596 14.1829 26.9622 14.2055 26.9098 14.2176C26.8981 14.2204 26.8886 14.2226 26.8811 14.2238C26.5161 14.285 26.2925 14.4697 26.0287 14.6874C26.0135 14.7 25.9982 14.7126 25.9827 14.7254C25.1396 15.419 24.4279 15.5324 23.6358 15.4988C23.3892 15.4884 23.135 15.4638 22.8668 15.4378C22.6201 15.4139 22.3617 15.3889 22.0865 15.3728L21.7503 15.3536C20.424 15.2864 19.0017 16.4168 18.7652 18.0223C18.5011 19.8145 19.5028 20.9122 20.1542 21.6261C20.3152 21.8025 20.4548 21.9555 20.5486 22.0897C20.6088 22.1707 20.6787 22.2847 20.6787 22.3933C20.6787 22.5229 20.5936 22.6255 20.5103 22.7131C19.1573 24.0865 18.7245 26.2687 19.2345 28.0873C19.2983 28.3141 19.3792 28.5313 19.4746 28.7389C20.6708 31.4983 24.3823 32.7835 28.0074 31.6147C30.5481 30.7955 32.7929 28.8159 33.25 26.0851C33.3588 25.3735 33.199 25.0987 32.9814 24.9661C32.751 24.8263 32.475 24.8749 32.475 24.8749C32.475 24.8749 32.3492 24.0247 31.993 23.2519C30.936 24.0751 29.5756 24.6535 28.5399 24.9469C26.8804 25.4168 25.0879 25.5998 23.373 25.3782C22.6775 25.2883 22.2102 25.228 22.0099 25.8703C24.2972 26.6971 26.7182 26.3431 26.7182 26.3431C26.765 26.3383 26.8069 26.3719 26.8118 26.4181ZM20.2125 10.3736C18.9616 11.0054 17.5648 12.1304 16.4305 13.4246C16.3904 13.4708 16.45 13.5344 16.4992 13.499C17.479 12.7952 18.8224 12.1412 20.5821 11.7176C22.5533 11.243 24.451 11.4422 25.6101 11.7044C25.6685 11.7176 25.7049 11.6186 25.6533 11.5898C24.8874 11.1656 23.7118 10.8776 22.8779 10.8716C22.8371 10.871 22.814 10.8242 22.8384 10.7918C22.9824 10.6004 23.18 10.4114 23.3605 10.2746C23.4006 10.2434 23.3763 10.1792 23.3252 10.1822C22.2878 10.2451 19.9183 11.3255 19.9268 11.2862C19.9876 10.9982 20.1791 10.6178 20.2781 10.4402C20.3019 10.3982 20.2557 10.352 20.2125 10.3736Z"
        fill="#0C111D"
      />
    </svg>
  );
}

function GoogleMeetIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="34"
      height="28"
      viewBox="0 0 34 28"
      fill="none"
    >
      <path
        d="M0.195312 25.5498C0.195312 26.7653 1.18832 27.7498 2.41179 27.7498H2.44365C1.20145 27.7498 0.195312 26.7653 0.195312 25.5498Z"
        fill="#FBBC05"
      />
      <path
        d="M19.2427 8.49984V14.2436L26.9864 7.99752V2.44996C26.9864 1.23448 25.9933 0.25 24.7699 0.25H7.99351L7.97852 8.49984H19.2427Z"
        fill="#FBBC05"
      />
      <path
        d="M19.2433 19.9898H7.96038L7.94727 27.7502H24.7705C25.9958 27.7502 26.987 26.7657 26.987 25.5502V20.5416L19.2433 14.2461V19.9898Z"
        fill="#34A853"
      />
      <path
        d="M7.99331 0.25L0.195312 8.49984H7.98019L7.99331 0.25Z"
        fill="#EA4335"
      />
      <path
        d="M0.195312 19.9893V25.5497C0.195312 26.7651 1.20145 27.7496 2.44365 27.7496H7.94647L7.95959 19.9893H0.195312Z"
        fill="#1967D2"
      />
      <path
        d="M7.98019 8.5H0.195312V19.9893H7.95959L7.98019 8.5Z"
        fill="#4285F4"
      />
      <path
        d="M33.7951 23.5941V4.71118C33.3586 2.20506 30.61 5.07784 30.61 5.07784L26.9883 7.99828V20.5399L32.1726 24.7546C34.0443 25.0003 33.7951 23.5941 33.7951 23.5941Z"
        fill="#34A853"
      />
      <path
        d="M19.2422 14.2437L26.9877 20.5411V7.99951L19.2422 14.2437Z"
        fill="#188038"
      />
    </svg>
  );
}

function ZoomIcon() {
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.5 14.5C2.5 10.2996 2.5 8.19941 3.31745 6.59507C4.0365 5.18386 5.18386 4.0365 6.59507 3.31745C8.19941 2.5 10.2996 2.5 14.5 2.5H25.5C29.7004 2.5 31.8006 2.5 33.4049 3.31745C34.8161 4.0365 35.9635 5.18386 36.6825 6.59507C37.5 8.19941 37.5 10.2996 37.5 14.5V25.5C37.5 29.7004 37.5 31.8006 36.6825 33.4049C35.9635 34.8161 34.8161 35.9635 33.4049 36.6825C31.8006 37.5 29.7004 37.5 25.5 37.5H14.5C10.2996 37.5 8.19941 37.5 6.59507 36.6825C5.18386 35.9635 4.0365 34.8161 3.31745 33.4049C2.5 31.8006 2.5 29.7004 2.5 25.5V14.5Z"
        fill="#4087FC"
      />
      <path
        d="M10.3333 12.5C9.45888 12.5 8.75 13.2995 8.75 14.2857V22.9464C8.75 25.4613 10.5577 27.5 12.7875 27.5L22.1667 27.4107C23.0411 27.4107 23.75 26.6113 23.75 25.625V16.875C23.75 14.3602 21.6048 12.5 19.375 12.5L10.3333 12.5Z"
        fill="white"
      />
      <path
        d="M25.8903 15.9095C25.3244 16.469 25 17.2764 25 18.125V21.7491C25 22.5978 25.3244 23.4052 25.8903 23.9647L29.411 27.0656C30.1274 27.7739 31.25 27.2002 31.25 26.1256V13.919C31.25 12.8444 30.1274 12.2707 29.411 12.979L25.8903 15.9095Z"
        fill="white"
      />
    </svg>
  );
}

function LoomIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
    >
      <path
        d="M37.5 18.1683H26.8379L36.0713 12.8367L34.2403 9.66333L25.0057 14.995L30.3356 5.76083L27.1633 3.92917L21.8322 13.161V2.5H18.1678V13.1622L12.8367 3.92917L9.66328 5.76083L14.9932 14.9927L5.76091 9.66333L3.92869 12.8367L13.1621 18.1683H2.5V21.8328H13.1609L3.92869 27.1645L5.75975 30.3378L14.9943 25.0062L9.66328 34.2403L12.8356 36.072L18.1678 26.839V37.5H21.8322V26.6103L27.2612 36.0137L30.2364 34.2963L24.8062 24.8907L34.2379 30.3367L36.0701 27.1633L26.8379 21.8317H37.4988V18.1683H37.5ZM20 24.9817C19.3459 24.9817 18.6981 24.8529 18.0937 24.6026C17.4894 24.3523 16.9402 23.9853 16.4776 23.5226C16.015 23.06 15.648 22.5107 15.3976 21.9062C15.1472 21.3017 15.0183 20.6538 15.0182 19.9994C15.0182 19.3451 15.1469 18.6971 15.3972 18.0925C15.6474 17.4879 16.0143 16.9386 16.4768 16.4759C16.9393 16.0131 17.4884 15.646 18.0927 15.3955C18.697 15.145 19.3447 15.0161 19.9988 15.016C21.3199 15.0158 22.587 15.5407 23.5212 16.475C24.4555 17.4094 24.9804 18.6767 24.9806 19.9982C24.9807 21.3198 24.4561 22.5872 23.522 23.5218C22.588 24.4564 21.3211 24.9815 20 24.9817Z"
        fill="#625DF5"
      />
    </svg>
  );
}

function LinearIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
    >
      <path
        d="M2.5 12.5625C2.5 9.00318 2.5 7.22352 3.20367 5.86891C3.79664 4.72739 4.72739 3.79664 5.86891 3.20367C7.22352 2.5 9.00318 2.5 12.5625 2.5H27.4375C30.9968 2.5 32.7765 2.5 34.1311 3.20367C35.2726 3.79664 36.2034 4.72739 36.7963 5.86891C37.5 7.22352 37.5 9.00318 37.5 12.5625V27.4375C37.5 30.9968 37.5 32.7765 36.7963 34.1311C36.2034 35.2726 35.2726 36.2034 34.1311 36.7963C32.7765 37.5 30.9968 37.5 27.4375 37.5H12.5625C9.00318 37.5 7.22352 37.5 5.86891 36.7963C4.72739 36.2034 3.79664 35.2726 3.20367 34.1311C2.5 32.7765 2.5 30.9968 2.5 27.4375V12.5625Z"
        fill="url(#paint0_linear_5622_4888)"
      />
      <path
        d="M18.2756 30.2419L9.75781 21.7241C10.4854 26.0779 13.9218 29.5144 18.2756 30.2419Z"
        fill="#F2F3F8"
      />
      <path
        d="M9.63086 19.422L20.5778 30.3689C21.2272 30.3332 21.8607 30.238 22.473 30.0884L9.91141 17.5269C9.7618 18.1391 9.66656 18.7726 9.63086 19.422Z"
        fill="#F2F3F8"
      />
      <path
        d="M10.459 15.8971L24.1047 29.5428C24.601 29.329 25.0774 29.0778 25.5301 28.7924L11.2093 14.4717C10.924 14.9244 10.6727 15.4007 10.459 15.8971Z"
        fill="#F2F3F8"
      />
      <path
        d="M12.1309 13.2208C14.0352 11.0128 16.8532 9.61523 19.9977 9.61523C25.733 9.61523 30.3823 14.2646 30.3823 19.9998C30.3823 23.1444 28.9847 25.9624 26.7768 27.8667L12.1309 13.2208Z"
        fill="#F2F3F8"
      />
      <defs>
        <linearGradient
          id="paint0_linear_5622_4888"
          x1="2.5"
          y1="37.5"
          x2="37.5"
          y2="2.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#29359B" />
          <stop offset="1" stopColor="#6068CA" />
        </linearGradient>
      </defs>
    </svg>
  );
}

function GmailIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="34"
      height="26"
      viewBox="0 0 34 26"
      fill="none"
    >
      <path
        d="M25.8983 2.73975L17.0938 9.61095L8.08789 2.73975V2.7416L8.09877 2.75089V12.3724L16.9922 19.3922L25.8983 12.6436V2.73975Z"
        fill="#EA4335"
      />
      <path
        d="M28.2107 1.06821L25.8984 2.73959V12.6434L33.1742 7.05731V3.69227C33.1742 3.69227 32.291 -1.11386 28.2107 1.06821Z"
        fill="#FBBC05"
      />
      <path
        d="M25.8984 12.6437V25.4892H31.475C31.475 25.4892 33.0618 25.3257 33.176 23.5169V7.05762L25.8984 12.6437Z"
        fill="#34A853"
      />
      <path
        d="M8.09918 25.4998V12.3721L8.08789 12.3628L8.09918 25.4998Z"
        fill="#C5221F"
      />
      <path
        d="M8.08912 2.7417L5.7896 1.07961C1.70921 -1.10246 0.824219 3.70181 0.824219 3.70181V7.06685L8.08912 12.3632V2.7417Z"
        fill="#C5221F"
      />
      <path
        d="M8.08789 2.74121V12.3628L8.09918 12.372V2.7505L8.08789 2.74121Z"
        fill="#C5221F"
      />
      <path
        d="M0.824219 7.06836V23.5277C0.936656 25.3383 2.52529 25.4999 2.52529 25.4999H8.10182L8.08912 12.3629L0.824219 7.06836Z"
        fill="#4285F4"
      />
    </svg>
  );
}

function TrelloIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M31.2667 5H8.75238C6.68 5 5 6.67893 5 8.75V31.25C5 33.3211 6.68 35 8.75238 35H31.2667C33.3316 34.9895 35 33.3137 35 31.25V8.75C35 6.68634 33.3316 5.01048 31.2667 5ZM11.25 8.75H16.25C17.6307 8.75 18.75 9.86929 18.75 11.25V28.75C18.75 30.1307 17.6307 31.25 16.25 31.25H11.25C9.86929 31.25 8.75 30.1307 8.75 28.75V11.25C8.75 9.86929 9.86929 8.75 11.25 8.75ZM28.75 8.75H23.75C22.3693 8.75 21.25 9.86929 21.25 11.25V20C21.25 21.3807 22.3693 22.5 23.75 22.5H28.75C30.1307 22.5 31.25 21.3807 31.25 20V11.25C31.25 9.86929 30.1307 8.75 28.75 8.75Z"
        fill="url(#paint0_linear_5622_2642)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_5622_2642"
          x1="20.0095"
          y1="35"
          x2="20.0095"
          y2="5"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#0052CC" />
          <stop offset="0.51698" stopColor="#217EF8" />
          <stop offset="1" stopColor="#2684FF" />
        </linearGradient>
      </defs>
    </svg>
  );
}

function NotionIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.145 36.5222L2.8458 31.1664C1.81141 29.8778 1.25 28.2933 1.25 26.6623V7.26873C1.25 4.8258 3.20448 2.79871 5.72589 2.62656L25.6652 1.26523C27.1137 1.16633 28.5535 1.55136 29.7434 2.35588L36.7491 7.09239C38.0031 7.94024 38.75 9.32623 38.75 10.8053V32.854C38.75 35.2447 36.8282 37.2241 34.3595 37.3761L12.2292 38.7383C10.2592 38.8596 8.35499 38.0296 7.145 36.5222Z"
        fill="white"
      />
      <path
        d="M14.0611 16.9733V16.7194C14.0611 16.0757 14.5766 15.542 15.2409 15.4976L20.0801 15.1745L26.7723 25.0293V16.3801L25.0497 16.1504V16.0298C25.0497 15.3787 25.577 14.8414 26.2498 14.8069L30.653 14.5813V15.215C30.653 15.5124 30.4318 15.7669 30.1286 15.8181L29.069 15.9974V30.0045L27.7392 30.4618C26.6283 30.8439 25.3914 30.4349 24.7555 29.4752L18.2586 19.6714V29.0286L20.2583 29.4113L20.2304 29.5968C20.1432 30.1778 19.6415 30.6172 19.0347 30.6442L14.0611 30.8656C13.9953 30.2408 14.4636 29.6818 15.1096 29.614L15.7638 29.5452V17.0689L14.0611 16.9733Z"
        fill="black"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M25.8446 3.70836L5.90532 5.06969C4.71096 5.15124 3.78516 6.11144 3.78516 7.26861V26.6622C3.78516 27.7495 4.15943 28.8059 4.84902 29.6649L9.14822 35.0208C9.84332 35.8867 10.9372 36.3635 12.0689 36.2938L34.1993 34.9316C35.3335 34.8617 36.2165 33.9523 36.2165 32.8538V10.8052C36.2165 10.1256 35.8733 9.48881 35.2972 9.09926L28.2916 4.36275C27.5776 3.88004 26.7137 3.64902 25.8446 3.70836ZM6.89406 7.57112C6.61674 7.36581 6.74872 6.93806 7.0971 6.91307L25.9787 5.55876C26.5803 5.51561 27.1781 5.68128 27.6646 6.02598L31.4531 8.71004C31.5969 8.81194 31.5294 9.03192 31.3514 9.0416L11.3558 10.1291C10.7507 10.162 10.1534 9.98422 9.67224 9.628L6.89406 7.57112ZM10.4179 13.5383C10.4179 12.8887 10.9427 12.3521 11.6138 12.3155L32.7548 11.1642C33.4088 11.1286 33.9594 11.6315 33.9594 12.2646V31.3565C33.9594 32.0049 33.4365 32.5409 32.7667 32.579L11.7596 33.7743C11.0317 33.8157 10.4179 33.2564 10.4179 32.5517V13.5383Z"
        fill="black"
      />
    </svg>
  );
}

function JiraIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
    >
      <path
        d="M35.0809 17.0206L19.5094 1.95987L18 0.5L6.27836 11.8371L0.919075 17.0206C0.360308 17.5617 0.360308 18.4383 0.919075 18.9794L11.6281 29.3371L18 35.5L29.7216 24.1629L29.9032 23.9873L35.0809 18.9794C35.6397 18.4383 35.6397 17.5617 35.0809 17.0206ZM18 23.1742L12.6503 18L18 12.8258L23.3497 18L18 23.1742Z"
        fill="#2684FF"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.9994 12.8257C14.4968 9.43759 14.4798 3.94993 17.9612 0.541504L6.25391 11.8601L12.6258 18.023L17.9994 12.8257Z"
        fill="url(#paint0_linear_5622_2518)"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M23.3641 17.9863L18 23.1744C19.6906 24.8086 20.6404 27.0256 20.6404 29.3373C20.6404 31.649 19.6906 33.866 18 35.5002L29.736 24.1492L23.3641 17.9863Z"
        fill="url(#paint1_linear_5622_2518)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_5622_2518"
          x1="17.0441"
          y1="7.5914"
          x2="8.12008"
          y2="11.4878"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.18" stopColor="#0052CC" />
          <stop offset="1" stopColor="#2684FF" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_5622_2518"
          x1="19.0222"
          y1="28.3487"
          x2="27.9302"
          y2="24.4799"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.18" stopColor="#0052CC" />
          <stop offset="1" stopColor="#2684FF" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export {
  MailchimpIcon,
  GoogleMeetIcon,
  ZoomIcon,
  LoomIcon,
  LinearIcon,
  GmailIcon,
  TrelloIcon,
  NotionIcon,
  JiraIcon,
};
