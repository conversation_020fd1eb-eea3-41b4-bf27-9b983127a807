---
type: "always_apply"
---

# Reglas de Environment

## Ambiente de Desarrollo

### Sistema Base (Cross-Platform)
- **Sistema operativo**: Windows, macOS, o Linux
- **Terminal**: Command-line interface principal configurado
- **Shell**: PowerShell (Windows), Zsh/Bash (Unix-like)
- **Variables entorno**: Usuario y sistema configuradas

### Herramientas Esenciales
- **IDE**: VS Code + extensiones (Augment Code, ESLint, Prettier, TypeScript)
- **Git**: Configuracion global apropiada por OS
- **Runtime**: Node.js via version manager (Volta, nvm)
- **Database**: PostgreSQL, MongoDB + GUI client
- **Container**: Docker Desktop o Podman

### Scripts de Automatizacion
- **Project init**: Estructura proyecto, Git init, archivos base
- **DB operations**: Backup, restore, migrations automatizadas
- **Dev shortcuts**: Aliases para comandos frecuentes
- **Environment switching**: Cambio rapido entre proyectos

## Containerizacion

### Docker Best Practices
- **Multi-stage builds**: Optimizar imagen size y seguridad
- **Base images**: Use official, minimal images (alpine)
- **Layer caching**: Ordenar comandos para maxima reutilizacion
- **Security**: Non-root user, minimal permissions
- **Health checks**: Container health monitoring

### Container Structure
```dockerfile
# Multi-stage example
FROM node:18-alpine AS dependencies
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
USER nextjs
WORKDIR /app
COPY --from=dependencies --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --chown=nextjs:nodejs . .
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1
EXPOSE 3000
CMD ["npm", "start"]
```

### Docker Compose Development
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
  
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: devdb
      POSTGRES_USER: dev
      POSTGRES_PASSWORD: devpass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dev -d devdb"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

## Multi-Sistema Config

### Multi-Idioma (i18n)
- **Detection order**: Accept-Language header → user preference → default
- **File structure**: Namespace organization (common, errors, business)
- **Fallback chain**: Specific locale → base language → default
- **Loading**: Lazy loading, cache warm-up

### Multi-Timezone
- **Storage**: UTC always in database
- **Display**: Convert to user timezone in presentation layer
- **Input**: Convert user input to UTC before storage
- **API**: ISO 8601 format with timezone info

### Multi-Currency
- **Configuration**: Symbol, decimal places, formatting rules
- **Exchange rates**: External API with caching and fallback
- **Storage**: Amount + currency code separate fields
- **Display**: Format according to locale conventions

## Variables Ambiente

### Environment Strategy
- **Template**: .env.example comprehensive template
- **Separation**: Local, staging, production configs
- **Hierarchy**: System → .env.local → .env → defaults
- **Validation**: Runtime validation required variables

### Security Management
- **Secrets**: External secret management (Vault, AWS Secrets)
- **Rotation**: Automated key/password rotation
- **Access control**: Role-based secret access
- **Audit**: Secret access logging

## Deployment y CI/CD

### Container Deployment
- **Image registry**: Private registry recommended
- **Tagging**: Semantic versioning + git commit SHA
- **Scanning**: Vulnerability scanning in CI pipeline
- **Orchestration**: Kubernetes or Docker Swarm
- **Rolling updates**: Zero-downtime deployments

### CI/CD Pipeline
```yaml
# GitHub Actions example
stages:
  test:
    - Unit tests
    - Integration tests
    - Security scan
  build:
    - Docker build multi-stage
    - Image vulnerability scan
    - Push to registry
  deploy:
    - Deploy to staging
    - Run smoke tests
    - Deploy to production (approval required)
```

### Infrastructure as Code
- **Terraform**: Infrastructure provisioning
- **Ansible**: Configuration management
- **Helm**: Kubernetes application deployment
- **Monitoring**: Prometheus + Grafana stack

## Monitoreo y Debug

### Observability
- **Logging**: Structured JSON logs (ELK/EFK stack)
- **Metrics**: Prometheus metrics collection
- **Tracing**: Distributed tracing (Jaeger/Zipkin)
- **Health**: Comprehensive health endpoints

### Development Debug
- **IDE integration**: Debug configuration per environment
- **Remote debugging**: Container debugging support
- **Log aggregation**: Centralized development logs
- **Performance profiling**: CPU/memory profiling tools

## Environment Checklist

### Development Setup
- Container runtime installed and configured
- IDE with necessary extensions
- Version manager for runtime (Node.js, Python)
- Database client tools
- Environment variables configured
- Docker compose services running

### Production Readiness
- Container images scanned for vulnerabilities
- Health checks responding correctly
- Monitoring and alerting configured
- Backup strategies implemented
- SSL certificates valid and monitored
- Load balancer health checks passing
- Database migrations applied
- Secret rotation procedures tested

### Container Best Practices
- Images built with multi-stage Dockerfile
- Non-root user configured
- Health checks implemented
- Resource limits defined
- Security scanning in CI pipeline
- Registry vulnerability scanning enabled